System.register(["cc"], function (_export, _context) {
  "use strict";

  var _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, Vec2, GizmoUtils, _crd;

  _export("GizmoUtils", void 0);

  return {
    setters: [function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      Vec2 = _cc.Vec2;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "cef3dvPqQRJKpshqVWxyy+Q", "GizmoUtils", undefined);

      /**
       * Utility class for common 2D gizmo drawing functions
       * Provides a comprehensive set of drawing primitives for gizmo visualization
       */
      __checkObsolete__(['_decorator', 'Graphics', 'Color', 'Vec2', 'Vec3']);

      _export("GizmoUtils", GizmoUtils = class GizmoUtils {
        /**
         * Draw a simple line between two points
         */
        static drawLine(graphics, startX, startY, endX, endY, color, lineWidth = 1) {
          graphics.strokeColor = color;
          graphics.lineWidth = lineWidth;
          graphics.moveTo(startX, startY);
          graphics.lineTo(endX, endY);
          graphics.stroke();
        }
        /**
         * Draw a circle (filled or outline)
         */


        static drawCircle(graphics, centerX, centerY, radius, color, filled = false, lineWidth = 1) {
          if (filled) {
            graphics.fillColor = color;
            graphics.circle(centerX, centerY, radius);
            graphics.fill();
          } else {
            graphics.strokeColor = color;
            graphics.lineWidth = lineWidth;
            graphics.circle(centerX, centerY, radius);
            graphics.stroke();
          }
        }
        /**
         * Draw a cross/plus symbol at the specified position
         */


        static drawCross(graphics, centerX, centerY, size, color, lineWidth = 2) {
          graphics.strokeColor = color;
          graphics.lineWidth = lineWidth; // Horizontal line

          graphics.moveTo(centerX - size, centerY);
          graphics.lineTo(centerX + size, centerY); // Vertical line

          graphics.moveTo(centerX, centerY - size);
          graphics.lineTo(centerX, centerY + size);
          graphics.stroke();
        }
        /**
         * Draw an arrow from start to end point
         */


        static drawArrow(graphics, startX, startY, endX, endY, color, arrowSize = 8, lineWidth = 2) {
          graphics.strokeColor = color;
          graphics.lineWidth = lineWidth; // Draw main line

          graphics.moveTo(startX, startY);
          graphics.lineTo(endX, endY); // Calculate arrow head

          const dx = endX - startX;
          const dy = endY - startY;
          const length = Math.sqrt(dx * dx + dy * dy);

          if (length > 0) {
            const dirX = dx / length;
            const dirY = dy / length; // Arrow head angle (30 degrees)

            const arrowAngle = Math.PI / 6; // Left arrow point

            const leftX = endX - arrowSize * (dirX * Math.cos(arrowAngle) - dirY * Math.sin(arrowAngle));
            const leftY = endY - arrowSize * (dirY * Math.cos(arrowAngle) + dirX * Math.sin(arrowAngle)); // Right arrow point

            const rightX = endX - arrowSize * (dirX * Math.cos(-arrowAngle) - dirY * Math.sin(-arrowAngle));
            const rightY = endY - arrowSize * (dirY * Math.cos(-arrowAngle) + dirX * Math.sin(-arrowAngle)); // Draw arrow head

            graphics.moveTo(endX, endY);
            graphics.lineTo(leftX, leftY);
            graphics.moveTo(endX, endY);
            graphics.lineTo(rightX, rightY);
          }

          graphics.stroke();
        }
        /**
         * Draw a rectangle (filled or outline)
         */


        static drawRect(graphics, x, y, width, height, color, filled = false, lineWidth = 1) {
          if (filled) {
            graphics.fillColor = color;
            graphics.rect(x, y, width, height);
            graphics.fill();
          } else {
            graphics.strokeColor = color;
            graphics.lineWidth = lineWidth;
            graphics.rect(x, y, width, height);
            graphics.stroke();
          }
        }
        /**
         * Draw a quadratic Bezier curve
         */


        static drawQuadraticBezier(graphics, startX, startY, controlX, controlY, endX, endY, color, segments = 20, lineWidth = 1) {
          graphics.strokeColor = color;
          graphics.lineWidth = lineWidth;
          graphics.moveTo(startX, startY);

          for (let i = 1; i <= segments; i++) {
            const t = i / segments;
            const x = (1 - t) * (1 - t) * startX + 2 * (1 - t) * t * controlX + t * t * endX;
            const y = (1 - t) * (1 - t) * startY + 2 * (1 - t) * t * controlY + t * t * endY;
            graphics.lineTo(x, y);
          }

          graphics.stroke();
        }
        /**
         * Draw a cubic Bezier curve
         */


        static drawCubicBezier(graphics, startX, startY, control1X, control1Y, control2X, control2Y, endX, endY, color, segments = 30, lineWidth = 1) {
          graphics.strokeColor = color;
          graphics.lineWidth = lineWidth;
          graphics.moveTo(startX, startY);

          for (let i = 1; i <= segments; i++) {
            const t = i / segments;
            const t2 = t * t;
            const t3 = t2 * t;
            const mt = 1 - t;
            const mt2 = mt * mt;
            const mt3 = mt2 * mt;
            const x = mt3 * startX + 3 * mt2 * t * control1X + 3 * mt * t2 * control2X + t3 * endX;
            const y = mt3 * startY + 3 * mt2 * t * control1Y + 3 * mt * t2 * control2Y + t3 * endY;
            graphics.lineTo(x, y);
          }

          graphics.stroke();
        }
        /**
         * Draw an arc (portion of a circle)
         */


        static drawArc(graphics, centerX, centerY, radius, startAngle, endAngle, color, segments = 20, lineWidth = 1) {
          graphics.strokeColor = color;
          graphics.lineWidth = lineWidth;
          const angleStep = (endAngle - startAngle) / segments;

          for (let i = 0; i <= segments; i++) {
            const angle = startAngle + angleStep * i;
            const x = centerX + Math.cos(angle) * radius;
            const y = centerY + Math.sin(angle) * radius;

            if (i === 0) {
              graphics.moveTo(x, y);
            } else {
              graphics.lineTo(x, y);
            }
          }

          graphics.stroke();
        }
        /**
         * Draw a polygon from an array of points
         */


        static drawPolygon(graphics, points, color, filled = false, lineWidth = 1) {
          if (points.length < 2) return;

          if (filled) {
            graphics.fillColor = color;
          } else {
            graphics.strokeColor = color;
            graphics.lineWidth = lineWidth;
          }

          graphics.moveTo(points[0].x, points[0].y);

          for (let i = 1; i < points.length; i++) {
            graphics.lineTo(points[i].x, points[i].y);
          } // Close the polygon


          graphics.lineTo(points[0].x, points[0].y);

          if (filled) {
            graphics.fill();
          } else {
            graphics.stroke();
          }
        }
        /**
         * Draw a dashed line
         */


        static drawDashedLine(graphics, startX, startY, endX, endY, color, dashLength = 5, gapLength = 3, lineWidth = 1) {
          graphics.strokeColor = color;
          graphics.lineWidth = lineWidth;
          const dx = endX - startX;
          const dy = endY - startY;
          const totalLength = Math.sqrt(dx * dx + dy * dy);
          const dirX = dx / totalLength;
          const dirY = dy / totalLength;
          let currentLength = 0;
          let isDash = true;

          while (currentLength < totalLength) {
            const segmentLength = isDash ? dashLength : gapLength;
            const nextLength = Math.min(currentLength + segmentLength, totalLength);
            const currentX = startX + dirX * currentLength;
            const currentY = startY + dirY * currentLength;
            const nextX = startX + dirX * nextLength;
            const nextY = startY + dirY * nextLength;

            if (isDash) {
              graphics.moveTo(currentX, currentY);
              graphics.lineTo(nextX, nextY);
            }

            currentLength = nextLength;
            isDash = !isDash;
          }

          graphics.stroke();
        }
        /**
         * Draw a grid
         */


        static drawGrid(graphics, centerX, centerY, width, height, cellSize, color, lineWidth = 1) {
          graphics.strokeColor = color;
          graphics.lineWidth = lineWidth;
          const halfWidth = width / 2;
          const halfHeight = height / 2;
          const left = centerX - halfWidth;
          const right = centerX + halfWidth;
          const top = centerY - halfHeight;
          const bottom = centerY + halfHeight; // Vertical lines

          for (let x = left; x <= right; x += cellSize) {
            graphics.moveTo(x, top);
            graphics.lineTo(x, bottom);
          } // Horizontal lines


          for (let y = top; y <= bottom; y += cellSize) {
            graphics.moveTo(left, y);
            graphics.lineTo(right, y);
          }

          graphics.stroke();
        }
        /**
         * Draw a diamond/rhombus shape
         */


        static drawDiamond(graphics, centerX, centerY, size, color, filled = false, lineWidth = 1) {
          const halfSize = size / 2;
          const points = [new Vec2(centerX, centerY - halfSize), // Top
          new Vec2(centerX + halfSize, centerY), // Right
          new Vec2(centerX, centerY + halfSize), // Bottom
          new Vec2(centerX - halfSize, centerY) // Left
          ];
          GizmoUtils.drawPolygon(graphics, points, color, filled, lineWidth);
        }
        /**
         * Draw a star shape
         */


        static drawStar(graphics, centerX, centerY, outerRadius, innerRadius, points, color, filled = false, lineWidth = 1) {
          const vertices = [];
          const angleStep = Math.PI * 2 / (points * 2);

          for (let i = 0; i < points * 2; i++) {
            const angle = i * angleStep - Math.PI / 2; // Start from top

            const radius = i % 2 === 0 ? outerRadius : innerRadius;
            const x = centerX + Math.cos(angle) * radius;
            const y = centerY + Math.sin(angle) * radius;
            vertices.push(new Vec2(x, y));
          }

          GizmoUtils.drawPolygon(graphics, vertices, color, filled, lineWidth);
        }
        /**
         * Draw a regular polygon (triangle, pentagon, hexagon, etc.)
         */


        static drawRegularPolygon(graphics, centerX, centerY, radius, sides, color, filled = false, lineWidth = 1) {
          if (sides < 3) return;
          const vertices = [];
          const angleStep = Math.PI * 2 / sides;

          for (let i = 0; i < sides; i++) {
            const angle = i * angleStep - Math.PI / 2; // Start from top

            const x = centerX + Math.cos(angle) * radius;
            const y = centerY + Math.sin(angle) * radius;
            vertices.push(new Vec2(x, y));
          }

          GizmoUtils.drawPolygon(graphics, vertices, color, filled, lineWidth);
        }
        /**
         * Draw a spiral
         */


        static drawSpiral(graphics, centerX, centerY, startRadius, endRadius, turns, color, segments = 100, lineWidth = 1) {
          graphics.strokeColor = color;
          graphics.lineWidth = lineWidth;
          const totalAngle = turns * Math.PI * 2;
          const radiusStep = (endRadius - startRadius) / segments;
          const angleStep = totalAngle / segments;

          for (let i = 0; i <= segments; i++) {
            const angle = i * angleStep;
            const radius = startRadius + i * radiusStep;
            const x = centerX + Math.cos(angle) * radius;
            const y = centerY + Math.sin(angle) * radius;

            if (i === 0) {
              graphics.moveTo(x, y);
            } else {
              graphics.lineTo(x, y);
            }
          }

          graphics.stroke();
        }
        /**
         * Draw a coordinate system (X and Y axes)
         */


        static drawCoordinateSystem(graphics, centerX, centerY, axisLength, color, lineWidth = 1, showArrows = true) {
          // X axis (horizontal)
          GizmoUtils.drawLine(graphics, centerX - axisLength, centerY, centerX + axisLength, centerY, color, lineWidth); // Y axis (vertical)

          GizmoUtils.drawLine(graphics, centerX, centerY - axisLength, centerX, centerY + axisLength, color, lineWidth);

          if (showArrows) {
            const arrowSize = Math.min(axisLength * 0.1, 10); // X axis arrow (pointing right)

            GizmoUtils.drawArrow(graphics, centerX + axisLength - arrowSize, centerY, centerX + axisLength, centerY, color, arrowSize, lineWidth); // Y axis arrow (pointing up)

            GizmoUtils.drawArrow(graphics, centerX, centerY - axisLength + arrowSize, centerX, centerY - axisLength, color, arrowSize, lineWidth);
          }
        }

      });

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=9f4b479b8a7ce740ec1fd8ec0e2bfde2857b833f.js.map