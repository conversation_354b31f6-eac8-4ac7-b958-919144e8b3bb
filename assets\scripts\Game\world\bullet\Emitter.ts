import { _decorator, Node } from 'cc';
import { CObject } from '../base/Object';
const { ccclass, property } = _decorator;

@ccclass('Emitter')
export abstract class Emitter extends CObject {

    @property({type: Node})
    bulletPrefab: Node = null;

    // 发射条数
    @property
    count: number = 1;

    // 子弹速度乘数
    @property
    speedMultiplier: number = 1;

    // 频率(间隔多少秒发射一次)
    @property
    frequency: number = 1;

    canTrigger(): boolean {
        // 检查是否可以触发发射
        // Override this method in subclasses to add custom trigger conditions
        return true;
    }

    /**
     * TODO: implement bullet emission logic in subclasses
     */
    protected emitBullet(): void {

    }

    /**
     * Internal method that wraps emitBullet for scheduling
     * This is a concrete method that can be safely scheduled
     */
    private _internalEmitBullet = (): void => {
        if (this.canTrigger()) {
            this.emitBullet();
        }
    }

    // Implementation of CObject abstract methods
    protected onObjectInit(): void {
        // Override in subclasses if needed
    }

    protected onObjectDestroy(): void {
        // Clean up any scheduled callbacks
        this.unschedule(this._internalEmitBullet);
    }

    protected onEnable(): void {
        // Start the emission schedule
        this.startEmission();
    }

    protected onDisable(): void {
        // Stop the emission schedule
        this.stopEmission();
    }

    /**
     * Start the emission schedule
     * This method can be overridden by subclasses if needed
     */
    protected startEmission(): void {
        this.schedule(this._internalEmitBullet, this.frequency);
    }

    /**
     * Stop the emission schedule
     * This method can be overridden by subclasses if needed
     */
    protected stopEmission(): void {
        this.unschedule(this._internalEmitBullet);
    }
}
