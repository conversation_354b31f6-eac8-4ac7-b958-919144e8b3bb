System.register(["cc"], function (_export, _context) {
  "use strict";

  var _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, Vec2, GizmoUtils, _crd;

  _export("GizmoUtils", void 0);

  return {
    setters: [function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      Vec2 = _cc.Vec2;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "cef3dvPqQRJKpshqVWxyy+Q", "GizmoUtils", undefined);

      /**
       * Utility class for common 2D gizmo drawing functions
       * Provides a comprehensive set of drawing primitives for gizmo visualization
       */
      __checkObsolete__(['_decorator', 'Graphics', 'Color', 'Vec2', 'Vec3']);

      _export("GizmoUtils", GizmoUtils = class GizmoUtils {
        /**
         * Draw a simple line between two points
         */
        static drawLine(graphics, startX, startY, endX, endY, color, lineWidth) {
          if (lineWidth === void 0) {
            lineWidth = 1;
          }

          graphics.strokeColor = color;
          graphics.lineWidth = lineWidth;
          graphics.moveTo(startX, startY);
          graphics.lineTo(endX, endY);
          graphics.stroke();
        }
        /**
         * Draw a circle (filled or outline)
         */


        static drawCircle(graphics, centerX, centerY, radius, color, filled, lineWidth) {
          if (filled === void 0) {
            filled = false;
          }

          if (lineWidth === void 0) {
            lineWidth = 1;
          }

          if (filled) {
            graphics.fillColor = color;
            graphics.circle(centerX, centerY, radius);
            graphics.fill();
          } else {
            graphics.strokeColor = color;
            graphics.lineWidth = lineWidth;
            graphics.circle(centerX, centerY, radius);
            graphics.stroke();
          }
        }
        /**
         * Draw a cross/plus symbol at the specified position
         */


        static drawCross(graphics, centerX, centerY, size, color, lineWidth) {
          if (lineWidth === void 0) {
            lineWidth = 2;
          }

          graphics.strokeColor = color;
          graphics.lineWidth = lineWidth; // Horizontal line

          graphics.moveTo(centerX - size, centerY);
          graphics.lineTo(centerX + size, centerY); // Vertical line

          graphics.moveTo(centerX, centerY - size);
          graphics.lineTo(centerX, centerY + size);
          graphics.stroke();
        }
        /**
         * Draw an arrow from start to end point
         */


        static drawArrow(graphics, startX, startY, endX, endY, color, arrowSize, lineWidth) {
          if (arrowSize === void 0) {
            arrowSize = 8;
          }

          if (lineWidth === void 0) {
            lineWidth = 2;
          }

          graphics.strokeColor = color;
          graphics.lineWidth = lineWidth; // Draw main line

          graphics.moveTo(startX, startY);
          graphics.lineTo(endX, endY); // Calculate arrow head

          var dx = endX - startX;
          var dy = endY - startY;
          var length = Math.sqrt(dx * dx + dy * dy);

          if (length > 0) {
            var dirX = dx / length;
            var dirY = dy / length; // Arrow head angle (30 degrees)

            var arrowAngle = Math.PI / 6; // Left arrow point

            var leftX = endX - arrowSize * (dirX * Math.cos(arrowAngle) - dirY * Math.sin(arrowAngle));
            var leftY = endY - arrowSize * (dirY * Math.cos(arrowAngle) + dirX * Math.sin(arrowAngle)); // Right arrow point

            var rightX = endX - arrowSize * (dirX * Math.cos(-arrowAngle) - dirY * Math.sin(-arrowAngle));
            var rightY = endY - arrowSize * (dirY * Math.cos(-arrowAngle) + dirX * Math.sin(-arrowAngle)); // Draw arrow head

            graphics.moveTo(endX, endY);
            graphics.lineTo(leftX, leftY);
            graphics.moveTo(endX, endY);
            graphics.lineTo(rightX, rightY);
          }

          graphics.stroke();
        }
        /**
         * Draw a rectangle (filled or outline)
         */


        static drawRect(graphics, x, y, width, height, color, filled, lineWidth) {
          if (filled === void 0) {
            filled = false;
          }

          if (lineWidth === void 0) {
            lineWidth = 1;
          }

          if (filled) {
            graphics.fillColor = color;
            graphics.rect(x, y, width, height);
            graphics.fill();
          } else {
            graphics.strokeColor = color;
            graphics.lineWidth = lineWidth;
            graphics.rect(x, y, width, height);
            graphics.stroke();
          }
        }
        /**
         * Draw a quadratic Bezier curve
         */


        static drawQuadraticBezier(graphics, startX, startY, controlX, controlY, endX, endY, color, segments, lineWidth) {
          if (segments === void 0) {
            segments = 20;
          }

          if (lineWidth === void 0) {
            lineWidth = 1;
          }

          graphics.strokeColor = color;
          graphics.lineWidth = lineWidth;
          graphics.moveTo(startX, startY);

          for (var i = 1; i <= segments; i++) {
            var t = i / segments;
            var x = (1 - t) * (1 - t) * startX + 2 * (1 - t) * t * controlX + t * t * endX;
            var y = (1 - t) * (1 - t) * startY + 2 * (1 - t) * t * controlY + t * t * endY;
            graphics.lineTo(x, y);
          }

          graphics.stroke();
        }
        /**
         * Draw a cubic Bezier curve
         */


        static drawCubicBezier(graphics, startX, startY, control1X, control1Y, control2X, control2Y, endX, endY, color, segments, lineWidth) {
          if (segments === void 0) {
            segments = 30;
          }

          if (lineWidth === void 0) {
            lineWidth = 1;
          }

          graphics.strokeColor = color;
          graphics.lineWidth = lineWidth;
          graphics.moveTo(startX, startY);

          for (var i = 1; i <= segments; i++) {
            var t = i / segments;
            var t2 = t * t;
            var t3 = t2 * t;
            var mt = 1 - t;
            var mt2 = mt * mt;
            var mt3 = mt2 * mt;
            var x = mt3 * startX + 3 * mt2 * t * control1X + 3 * mt * t2 * control2X + t3 * endX;
            var y = mt3 * startY + 3 * mt2 * t * control1Y + 3 * mt * t2 * control2Y + t3 * endY;
            graphics.lineTo(x, y);
          }

          graphics.stroke();
        }
        /**
         * Draw an arc (portion of a circle)
         */


        static drawArc(graphics, centerX, centerY, radius, startAngle, endAngle, color, segments, lineWidth) {
          if (segments === void 0) {
            segments = 20;
          }

          if (lineWidth === void 0) {
            lineWidth = 1;
          }

          graphics.strokeColor = color;
          graphics.lineWidth = lineWidth;
          var angleStep = (endAngle - startAngle) / segments;

          for (var i = 0; i <= segments; i++) {
            var angle = startAngle + angleStep * i;
            var x = centerX + Math.cos(angle) * radius;
            var y = centerY + Math.sin(angle) * radius;

            if (i === 0) {
              graphics.moveTo(x, y);
            } else {
              graphics.lineTo(x, y);
            }
          }

          graphics.stroke();
        }
        /**
         * Draw a polygon from an array of points
         */


        static drawPolygon(graphics, points, color, filled, lineWidth) {
          if (filled === void 0) {
            filled = false;
          }

          if (lineWidth === void 0) {
            lineWidth = 1;
          }

          if (points.length < 2) return;

          if (filled) {
            graphics.fillColor = color;
          } else {
            graphics.strokeColor = color;
            graphics.lineWidth = lineWidth;
          }

          graphics.moveTo(points[0].x, points[0].y);

          for (var i = 1; i < points.length; i++) {
            graphics.lineTo(points[i].x, points[i].y);
          } // Close the polygon


          graphics.lineTo(points[0].x, points[0].y);

          if (filled) {
            graphics.fill();
          } else {
            graphics.stroke();
          }
        }
        /**
         * Draw a dashed line
         */


        static drawDashedLine(graphics, startX, startY, endX, endY, color, dashLength, gapLength, lineWidth) {
          if (dashLength === void 0) {
            dashLength = 5;
          }

          if (gapLength === void 0) {
            gapLength = 3;
          }

          if (lineWidth === void 0) {
            lineWidth = 1;
          }

          graphics.strokeColor = color;
          graphics.lineWidth = lineWidth;
          var dx = endX - startX;
          var dy = endY - startY;
          var totalLength = Math.sqrt(dx * dx + dy * dy);
          var dirX = dx / totalLength;
          var dirY = dy / totalLength;
          var currentLength = 0;
          var isDash = true;

          while (currentLength < totalLength) {
            var segmentLength = isDash ? dashLength : gapLength;
            var nextLength = Math.min(currentLength + segmentLength, totalLength);
            var currentX = startX + dirX * currentLength;
            var currentY = startY + dirY * currentLength;
            var nextX = startX + dirX * nextLength;
            var nextY = startY + dirY * nextLength;

            if (isDash) {
              graphics.moveTo(currentX, currentY);
              graphics.lineTo(nextX, nextY);
            }

            currentLength = nextLength;
            isDash = !isDash;
          }

          graphics.stroke();
        }
        /**
         * Draw a grid
         */


        static drawGrid(graphics, centerX, centerY, width, height, cellSize, color, lineWidth) {
          if (lineWidth === void 0) {
            lineWidth = 1;
          }

          graphics.strokeColor = color;
          graphics.lineWidth = lineWidth;
          var halfWidth = width / 2;
          var halfHeight = height / 2;
          var left = centerX - halfWidth;
          var right = centerX + halfWidth;
          var top = centerY - halfHeight;
          var bottom = centerY + halfHeight; // Vertical lines

          for (var x = left; x <= right; x += cellSize) {
            graphics.moveTo(x, top);
            graphics.lineTo(x, bottom);
          } // Horizontal lines


          for (var y = top; y <= bottom; y += cellSize) {
            graphics.moveTo(left, y);
            graphics.lineTo(right, y);
          }

          graphics.stroke();
        }
        /**
         * Draw a diamond/rhombus shape
         */


        static drawDiamond(graphics, centerX, centerY, size, color, filled, lineWidth) {
          if (filled === void 0) {
            filled = false;
          }

          if (lineWidth === void 0) {
            lineWidth = 1;
          }

          var halfSize = size / 2;
          var points = [new Vec2(centerX, centerY - halfSize), // Top
          new Vec2(centerX + halfSize, centerY), // Right
          new Vec2(centerX, centerY + halfSize), // Bottom
          new Vec2(centerX - halfSize, centerY) // Left
          ];
          GizmoUtils.drawPolygon(graphics, points, color, filled, lineWidth);
        }
        /**
         * Draw a star shape
         */


        static drawStar(graphics, centerX, centerY, outerRadius, innerRadius, points, color, filled, lineWidth) {
          if (filled === void 0) {
            filled = false;
          }

          if (lineWidth === void 0) {
            lineWidth = 1;
          }

          var vertices = [];
          var angleStep = Math.PI * 2 / (points * 2);

          for (var i = 0; i < points * 2; i++) {
            var angle = i * angleStep - Math.PI / 2; // Start from top

            var radius = i % 2 === 0 ? outerRadius : innerRadius;
            var x = centerX + Math.cos(angle) * radius;
            var y = centerY + Math.sin(angle) * radius;
            vertices.push(new Vec2(x, y));
          }

          GizmoUtils.drawPolygon(graphics, vertices, color, filled, lineWidth);
        }
        /**
         * Draw a regular polygon (triangle, pentagon, hexagon, etc.)
         */


        static drawRegularPolygon(graphics, centerX, centerY, radius, sides, color, filled, lineWidth) {
          if (filled === void 0) {
            filled = false;
          }

          if (lineWidth === void 0) {
            lineWidth = 1;
          }

          if (sides < 3) return;
          var vertices = [];
          var angleStep = Math.PI * 2 / sides;

          for (var i = 0; i < sides; i++) {
            var angle = i * angleStep - Math.PI / 2; // Start from top

            var x = centerX + Math.cos(angle) * radius;
            var y = centerY + Math.sin(angle) * radius;
            vertices.push(new Vec2(x, y));
          }

          GizmoUtils.drawPolygon(graphics, vertices, color, filled, lineWidth);
        }
        /**
         * Draw a spiral
         */


        static drawSpiral(graphics, centerX, centerY, startRadius, endRadius, turns, color, segments, lineWidth) {
          if (segments === void 0) {
            segments = 100;
          }

          if (lineWidth === void 0) {
            lineWidth = 1;
          }

          graphics.strokeColor = color;
          graphics.lineWidth = lineWidth;
          var totalAngle = turns * Math.PI * 2;
          var radiusStep = (endRadius - startRadius) / segments;
          var angleStep = totalAngle / segments;

          for (var i = 0; i <= segments; i++) {
            var angle = i * angleStep;
            var radius = startRadius + i * radiusStep;
            var x = centerX + Math.cos(angle) * radius;
            var y = centerY + Math.sin(angle) * radius;

            if (i === 0) {
              graphics.moveTo(x, y);
            } else {
              graphics.lineTo(x, y);
            }
          }

          graphics.stroke();
        }
        /**
         * Draw a coordinate system (X and Y axes)
         */


        static drawCoordinateSystem(graphics, centerX, centerY, axisLength, color, lineWidth, showArrows) {
          if (lineWidth === void 0) {
            lineWidth = 1;
          }

          if (showArrows === void 0) {
            showArrows = true;
          }

          // X axis (horizontal)
          GizmoUtils.drawLine(graphics, centerX - axisLength, centerY, centerX + axisLength, centerY, color, lineWidth); // Y axis (vertical)

          GizmoUtils.drawLine(graphics, centerX, centerY - axisLength, centerX, centerY + axisLength, color, lineWidth);

          if (showArrows) {
            var arrowSize = Math.min(axisLength * 0.1, 10); // X axis arrow (pointing right)

            GizmoUtils.drawArrow(graphics, centerX + axisLength - arrowSize, centerY, centerX + axisLength, centerY, color, arrowSize, lineWidth); // Y axis arrow (pointing up)

            GizmoUtils.drawArrow(graphics, centerX, centerY - axisLength + arrowSize, centerX, centerY - axisLength, color, arrowSize, lineWidth);
          }
        }

      });

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=9f4b479b8a7ce740ec1fd8ec0e2bfde2857b833f.js.map