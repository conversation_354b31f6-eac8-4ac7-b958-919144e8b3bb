System.register(["__unresolved_0", "cc", "__unresolved_1"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Node, CObject, _dec, _dec2, _class, _class2, _descriptor, _descriptor2, _descriptor3, _descriptor4, _crd, ccclass, property, Emitter;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfCObject(extras) {
    _reporterNs.report("CObject", "../base/Object", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Node = _cc.Node;
    }, function (_unresolved_2) {
      CObject = _unresolved_2.CObject;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "2564dArcRFKZKoo3odCQrHw", "Emitter", undefined);

      __checkObsolete__(['_decorator', 'Node']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("Emitter", Emitter = (_dec = ccclass('Emitter'), _dec2 = property({
        type: Node
      }), _dec(_class = (_class2 = class Emitter extends (_crd && CObject === void 0 ? (_reportPossibleCrUseOfCObject({
        error: Error()
      }), CObject) : CObject) {
        constructor() {
          super(...arguments);

          _initializerDefineProperty(this, "bulletPrefab", _descriptor, this);

          // 发射条数
          _initializerDefineProperty(this, "count", _descriptor2, this);

          // 子弹速度乘数
          _initializerDefineProperty(this, "speedMultiplier", _descriptor3, this);

          // 频率(间隔多少秒发射一次)
          _initializerDefineProperty(this, "frequency", _descriptor4, this);

          /**
           * Internal method that wraps emitBullet for scheduling
           * This is a concrete method that can be safely scheduled
           */
          this._internalEmitBullet = () => {
            if (this.canTrigger()) {
              this.emitBullet();
            }
          };
        }

        canTrigger() {
          // 检查是否可以触发发射
          // Override this method in subclasses to add custom trigger conditions
          return true;
        }
        /**
         * TODO: implement bullet emission logic in subclasses
         */


        emitBullet() {}

        // Implementation of CObject abstract methods
        onObjectInit() {// Override in subclasses if needed
        }

        onObjectDestroy() {
          // Clean up any scheduled callbacks
          this.unschedule(this._internalEmitBullet);
        }

        onEnable() {
          // Start the emission schedule
          this.startEmission();
        }

        onDisable() {
          // Stop the emission schedule
          this.stopEmission();
        }
        /**
         * Start the emission schedule
         * This method can be overridden by subclasses if needed
         */


        startEmission() {
          this.schedule(this._internalEmitBullet, this.frequency);
        }
        /**
         * Stop the emission schedule
         * This method can be overridden by subclasses if needed
         */


        stopEmission() {
          this.unschedule(this._internalEmitBullet);
        }

      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "bulletPrefab", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "count", [property], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return 1;
        }
      }), _descriptor3 = _applyDecoratedDescriptor(_class2.prototype, "speedMultiplier", [property], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return 1;
        }
      }), _descriptor4 = _applyDecoratedDescriptor(_class2.prototype, "frequency", [property], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return 1;
        }
      })), _class2)) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=10408dcd89246d6e37923b5ae6ec4a0179ea11e5.js.map