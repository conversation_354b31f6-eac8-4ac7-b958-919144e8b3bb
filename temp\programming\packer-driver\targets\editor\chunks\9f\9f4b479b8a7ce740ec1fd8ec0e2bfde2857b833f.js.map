{"version": 3, "sources": ["file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/gizmos/GizmoUtils.ts"], "names": ["Gizmo<PERSON><PERSON>s", "Vec2", "drawLine", "graphics", "startX", "startY", "endX", "endY", "color", "lineWidth", "strokeColor", "moveTo", "lineTo", "stroke", "drawCircle", "centerX", "centerY", "radius", "filled", "fillColor", "circle", "fill", "drawCross", "size", "drawArrow", "arrowSize", "dx", "dy", "length", "Math", "sqrt", "dirX", "dirY", "arrowAngle", "PI", "leftX", "cos", "sin", "leftY", "rightX", "rightY", "drawRect", "x", "y", "width", "height", "rect", "drawQuadraticBezier", "controlX", "controlY", "segments", "i", "t", "drawCubicBezier", "control1X", "control1Y", "control2X", "control2Y", "t2", "t3", "mt", "mt2", "mt3", "drawArc", "startAngle", "endAngle", "angleStep", "angle", "drawPolygon", "points", "drawDashedLine", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "totalLength", "<PERSON><PERSON><PERSON><PERSON>", "isDash", "segmentLength", "<PERSON><PERSON><PERSON><PERSON>", "min", "currentX", "currentY", "nextX", "nextY", "drawGrid", "cellSize", "halfWidth", "halfHeight", "left", "right", "top", "bottom", "draw<PERSON><PERSON><PERSON>", "halfSize", "drawStar", "outerRadius", "innerRadius", "vertices", "push", "drawRegularPolygon", "sides", "drawSpiral", "startRadius", "endRadius", "turns", "totalAngle", "radiusStep", "drawCoordinateSystem", "axisLength", "showArrows"], "mappings": ";;;wEAMaA,U;;;;;;;;;AANyBC,MAAAA,I,OAAAA,I;;;;;;;AAEtC;AACA;AACA;AACA;;;4BACaD,U,GAAN,MAAMA,UAAN,CAAiB;AAEpB;AACJ;AACA;AACmB,eAARE,QAAQ,CAACC,QAAD,EAAqBC,MAArB,EAAqCC,MAArC,EAAqDC,IAArD,EAAmEC,IAAnE,EAAiFC,KAAjF,EAA+FC,SAAiB,GAAG,CAAnH,EAA4H;AACvIN,UAAAA,QAAQ,CAACO,WAAT,GAAuBF,KAAvB;AACAL,UAAAA,QAAQ,CAACM,SAAT,GAAqBA,SAArB;AAEAN,UAAAA,QAAQ,CAACQ,MAAT,CAAgBP,MAAhB,EAAwBC,MAAxB;AACAF,UAAAA,QAAQ,CAACS,MAAT,CAAgBN,IAAhB,EAAsBC,IAAtB;AACAJ,UAAAA,QAAQ,CAACU,MAAT;AACH;AAED;AACJ;AACA;;;AACqB,eAAVC,UAAU,CAACX,QAAD,EAAqBY,OAArB,EAAsCC,OAAtC,EAAuDC,MAAvD,EAAuET,KAAvE,EAAqFU,MAAe,GAAG,KAAvG,EAA8GT,SAAiB,GAAG,CAAlI,EAA2I;AACxJ,cAAIS,MAAJ,EAAY;AACRf,YAAAA,QAAQ,CAACgB,SAAT,GAAqBX,KAArB;AACAL,YAAAA,QAAQ,CAACiB,MAAT,CAAgBL,OAAhB,EAAyBC,OAAzB,EAAkCC,MAAlC;AACAd,YAAAA,QAAQ,CAACkB,IAAT;AACH,WAJD,MAIO;AACHlB,YAAAA,QAAQ,CAACO,WAAT,GAAuBF,KAAvB;AACAL,YAAAA,QAAQ,CAACM,SAAT,GAAqBA,SAArB;AACAN,YAAAA,QAAQ,CAACiB,MAAT,CAAgBL,OAAhB,EAAyBC,OAAzB,EAAkCC,MAAlC;AACAd,YAAAA,QAAQ,CAACU,MAAT;AACH;AACJ;AAED;AACJ;AACA;;;AACoB,eAATS,SAAS,CAACnB,QAAD,EAAqBY,OAArB,EAAsCC,OAAtC,EAAuDO,IAAvD,EAAqEf,KAArE,EAAmFC,SAAiB,GAAG,CAAvG,EAAgH;AAC5HN,UAAAA,QAAQ,CAACO,WAAT,GAAuBF,KAAvB;AACAL,UAAAA,QAAQ,CAACM,SAAT,GAAqBA,SAArB,CAF4H,CAI5H;;AACAN,UAAAA,QAAQ,CAACQ,MAAT,CAAgBI,OAAO,GAAGQ,IAA1B,EAAgCP,OAAhC;AACAb,UAAAA,QAAQ,CAACS,MAAT,CAAgBG,OAAO,GAAGQ,IAA1B,EAAgCP,OAAhC,EAN4H,CAQ5H;;AACAb,UAAAA,QAAQ,CAACQ,MAAT,CAAgBI,OAAhB,EAAyBC,OAAO,GAAGO,IAAnC;AACApB,UAAAA,QAAQ,CAACS,MAAT,CAAgBG,OAAhB,EAAyBC,OAAO,GAAGO,IAAnC;AAEApB,UAAAA,QAAQ,CAACU,MAAT;AACH;AAED;AACJ;AACA;;;AACoB,eAATW,SAAS,CAACrB,QAAD,EAAqBC,MAArB,EAAqCC,MAArC,EAAqDC,IAArD,EAAmEC,IAAnE,EAAiFC,KAAjF,EAA+FiB,SAAiB,GAAG,CAAnH,EAAsHhB,SAAiB,GAAG,CAA1I,EAAmJ;AAC/JN,UAAAA,QAAQ,CAACO,WAAT,GAAuBF,KAAvB;AACAL,UAAAA,QAAQ,CAACM,SAAT,GAAqBA,SAArB,CAF+J,CAI/J;;AACAN,UAAAA,QAAQ,CAACQ,MAAT,CAAgBP,MAAhB,EAAwBC,MAAxB;AACAF,UAAAA,QAAQ,CAACS,MAAT,CAAgBN,IAAhB,EAAsBC,IAAtB,EAN+J,CAQ/J;;AACA,gBAAMmB,EAAE,GAAGpB,IAAI,GAAGF,MAAlB;AACA,gBAAMuB,EAAE,GAAGpB,IAAI,GAAGF,MAAlB;AACA,gBAAMuB,MAAM,GAAGC,IAAI,CAACC,IAAL,CAAUJ,EAAE,GAAGA,EAAL,GAAUC,EAAE,GAAGA,EAAzB,CAAf;;AAEA,cAAIC,MAAM,GAAG,CAAb,EAAgB;AACZ,kBAAMG,IAAI,GAAGL,EAAE,GAAGE,MAAlB;AACA,kBAAMI,IAAI,GAAGL,EAAE,GAAGC,MAAlB,CAFY,CAIZ;;AACA,kBAAMK,UAAU,GAAGJ,IAAI,CAACK,EAAL,GAAU,CAA7B,CALY,CAOZ;;AACA,kBAAMC,KAAK,GAAG7B,IAAI,GAAGmB,SAAS,IAAIM,IAAI,GAAGF,IAAI,CAACO,GAAL,CAASH,UAAT,CAAP,GAA8BD,IAAI,GAAGH,IAAI,CAACQ,GAAL,CAASJ,UAAT,CAAzC,CAA9B;AACA,kBAAMK,KAAK,GAAG/B,IAAI,GAAGkB,SAAS,IAAIO,IAAI,GAAGH,IAAI,CAACO,GAAL,CAASH,UAAT,CAAP,GAA8BF,IAAI,GAAGF,IAAI,CAACQ,GAAL,CAASJ,UAAT,CAAzC,CAA9B,CATY,CAWZ;;AACA,kBAAMM,MAAM,GAAGjC,IAAI,GAAGmB,SAAS,IAAIM,IAAI,GAAGF,IAAI,CAACO,GAAL,CAAS,CAACH,UAAV,CAAP,GAA+BD,IAAI,GAAGH,IAAI,CAACQ,GAAL,CAAS,CAACJ,UAAV,CAA1C,CAA/B;AACA,kBAAMO,MAAM,GAAGjC,IAAI,GAAGkB,SAAS,IAAIO,IAAI,GAAGH,IAAI,CAACO,GAAL,CAAS,CAACH,UAAV,CAAP,GAA+BF,IAAI,GAAGF,IAAI,CAACQ,GAAL,CAAS,CAACJ,UAAV,CAA1C,CAA/B,CAbY,CAeZ;;AACA9B,YAAAA,QAAQ,CAACQ,MAAT,CAAgBL,IAAhB,EAAsBC,IAAtB;AACAJ,YAAAA,QAAQ,CAACS,MAAT,CAAgBuB,KAAhB,EAAuBG,KAAvB;AACAnC,YAAAA,QAAQ,CAACQ,MAAT,CAAgBL,IAAhB,EAAsBC,IAAtB;AACAJ,YAAAA,QAAQ,CAACS,MAAT,CAAgB2B,MAAhB,EAAwBC,MAAxB;AACH;;AAEDrC,UAAAA,QAAQ,CAACU,MAAT;AACH;AAED;AACJ;AACA;;;AACmB,eAAR4B,QAAQ,CAACtC,QAAD,EAAqBuC,CAArB,EAAgCC,CAAhC,EAA2CC,KAA3C,EAA0DC,MAA1D,EAA0ErC,KAA1E,EAAwFU,MAAe,GAAG,KAA1G,EAAiHT,SAAiB,GAAG,CAArI,EAA8I;AACzJ,cAAIS,MAAJ,EAAY;AACRf,YAAAA,QAAQ,CAACgB,SAAT,GAAqBX,KAArB;AACAL,YAAAA,QAAQ,CAAC2C,IAAT,CAAcJ,CAAd,EAAiBC,CAAjB,EAAoBC,KAApB,EAA2BC,MAA3B;AACA1C,YAAAA,QAAQ,CAACkB,IAAT;AACH,WAJD,MAIO;AACHlB,YAAAA,QAAQ,CAACO,WAAT,GAAuBF,KAAvB;AACAL,YAAAA,QAAQ,CAACM,SAAT,GAAqBA,SAArB;AACAN,YAAAA,QAAQ,CAAC2C,IAAT,CAAcJ,CAAd,EAAiBC,CAAjB,EAAoBC,KAApB,EAA2BC,MAA3B;AACA1C,YAAAA,QAAQ,CAACU,MAAT;AACH;AACJ;AAED;AACJ;AACA;;;AAC8B,eAAnBkC,mBAAmB,CAAC5C,QAAD,EAAqBC,MAArB,EAAqCC,MAArC,EAAqD2C,QAArD,EAAuEC,QAAvE,EAAyF3C,IAAzF,EAAuGC,IAAvG,EAAqHC,KAArH,EAAmI0C,QAAgB,GAAG,EAAtJ,EAA0JzC,SAAiB,GAAG,CAA9K,EAAuL;AAC7MN,UAAAA,QAAQ,CAACO,WAAT,GAAuBF,KAAvB;AACAL,UAAAA,QAAQ,CAACM,SAAT,GAAqBA,SAArB;AAEAN,UAAAA,QAAQ,CAACQ,MAAT,CAAgBP,MAAhB,EAAwBC,MAAxB;;AAEA,eAAK,IAAI8C,CAAC,GAAG,CAAb,EAAgBA,CAAC,IAAID,QAArB,EAA+BC,CAAC,EAAhC,EAAoC;AAChC,kBAAMC,CAAC,GAAGD,CAAC,GAAGD,QAAd;AACA,kBAAMR,CAAC,GAAG,CAAC,IAAIU,CAAL,KAAW,IAAIA,CAAf,IAAoBhD,MAApB,GAA6B,KAAK,IAAIgD,CAAT,IAAcA,CAAd,GAAkBJ,QAA/C,GAA0DI,CAAC,GAAGA,CAAJ,GAAQ9C,IAA5E;AACA,kBAAMqC,CAAC,GAAG,CAAC,IAAIS,CAAL,KAAW,IAAIA,CAAf,IAAoB/C,MAApB,GAA6B,KAAK,IAAI+C,CAAT,IAAcA,CAAd,GAAkBH,QAA/C,GAA0DG,CAAC,GAAGA,CAAJ,GAAQ7C,IAA5E;AACAJ,YAAAA,QAAQ,CAACS,MAAT,CAAgB8B,CAAhB,EAAmBC,CAAnB;AACH;;AAEDxC,UAAAA,QAAQ,CAACU,MAAT;AACH;AAED;AACJ;AACA;;;AAC0B,eAAfwC,eAAe,CAAClD,QAAD,EAAqBC,MAArB,EAAqCC,MAArC,EAAqDiD,SAArD,EAAwEC,SAAxE,EAA2FC,SAA3F,EAA8GC,SAA9G,EAAiInD,IAAjI,EAA+IC,IAA/I,EAA6JC,KAA7J,EAA2K0C,QAAgB,GAAG,EAA9L,EAAkMzC,SAAiB,GAAG,CAAtN,EAA+N;AACjPN,UAAAA,QAAQ,CAACO,WAAT,GAAuBF,KAAvB;AACAL,UAAAA,QAAQ,CAACM,SAAT,GAAqBA,SAArB;AAEAN,UAAAA,QAAQ,CAACQ,MAAT,CAAgBP,MAAhB,EAAwBC,MAAxB;;AAEA,eAAK,IAAI8C,CAAC,GAAG,CAAb,EAAgBA,CAAC,IAAID,QAArB,EAA+BC,CAAC,EAAhC,EAAoC;AAChC,kBAAMC,CAAC,GAAGD,CAAC,GAAGD,QAAd;AACA,kBAAMQ,EAAE,GAAGN,CAAC,GAAGA,CAAf;AACA,kBAAMO,EAAE,GAAGD,EAAE,GAAGN,CAAhB;AACA,kBAAMQ,EAAE,GAAG,IAAIR,CAAf;AACA,kBAAMS,GAAG,GAAGD,EAAE,GAAGA,EAAjB;AACA,kBAAME,GAAG,GAAGD,GAAG,GAAGD,EAAlB;AAEA,kBAAMlB,CAAC,GAAGoB,GAAG,GAAG1D,MAAN,GAAe,IAAIyD,GAAJ,GAAUT,CAAV,GAAcE,SAA7B,GAAyC,IAAIM,EAAJ,GAASF,EAAT,GAAcF,SAAvD,GAAmEG,EAAE,GAAGrD,IAAlF;AACA,kBAAMqC,CAAC,GAAGmB,GAAG,GAAGzD,MAAN,GAAe,IAAIwD,GAAJ,GAAUT,CAAV,GAAcG,SAA7B,GAAyC,IAAIK,EAAJ,GAASF,EAAT,GAAcD,SAAvD,GAAmEE,EAAE,GAAGpD,IAAlF;AAEAJ,YAAAA,QAAQ,CAACS,MAAT,CAAgB8B,CAAhB,EAAmBC,CAAnB;AACH;;AAEDxC,UAAAA,QAAQ,CAACU,MAAT;AACH;AAED;AACJ;AACA;;;AACkB,eAAPkD,OAAO,CAAC5D,QAAD,EAAqBY,OAArB,EAAsCC,OAAtC,EAAuDC,MAAvD,EAAuE+C,UAAvE,EAA2FC,QAA3F,EAA6GzD,KAA7G,EAA2H0C,QAAgB,GAAG,EAA9I,EAAkJzC,SAAiB,GAAG,CAAtK,EAA+K;AACzLN,UAAAA,QAAQ,CAACO,WAAT,GAAuBF,KAAvB;AACAL,UAAAA,QAAQ,CAACM,SAAT,GAAqBA,SAArB;AAEA,gBAAMyD,SAAS,GAAG,CAACD,QAAQ,GAAGD,UAAZ,IAA0Bd,QAA5C;;AAEA,eAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,IAAID,QAArB,EAA+BC,CAAC,EAAhC,EAAoC;AAChC,kBAAMgB,KAAK,GAAGH,UAAU,GAAGE,SAAS,GAAGf,CAAvC;AACA,kBAAMT,CAAC,GAAG3B,OAAO,GAAGc,IAAI,CAACO,GAAL,CAAS+B,KAAT,IAAkBlD,MAAtC;AACA,kBAAM0B,CAAC,GAAG3B,OAAO,GAAGa,IAAI,CAACQ,GAAL,CAAS8B,KAAT,IAAkBlD,MAAtC;;AAEA,gBAAIkC,CAAC,KAAK,CAAV,EAAa;AACThD,cAAAA,QAAQ,CAACQ,MAAT,CAAgB+B,CAAhB,EAAmBC,CAAnB;AACH,aAFD,MAEO;AACHxC,cAAAA,QAAQ,CAACS,MAAT,CAAgB8B,CAAhB,EAAmBC,CAAnB;AACH;AACJ;;AAEDxC,UAAAA,QAAQ,CAACU,MAAT;AACH;AAED;AACJ;AACA;;;AACsB,eAAXuD,WAAW,CAACjE,QAAD,EAAqBkE,MAArB,EAAqC7D,KAArC,EAAmDU,MAAe,GAAG,KAArE,EAA4ET,SAAiB,GAAG,CAAhG,EAAyG;AACvH,cAAI4D,MAAM,CAACzC,MAAP,GAAgB,CAApB,EAAuB;;AAEvB,cAAIV,MAAJ,EAAY;AACRf,YAAAA,QAAQ,CAACgB,SAAT,GAAqBX,KAArB;AACH,WAFD,MAEO;AACHL,YAAAA,QAAQ,CAACO,WAAT,GAAuBF,KAAvB;AACAL,YAAAA,QAAQ,CAACM,SAAT,GAAqBA,SAArB;AACH;;AAEDN,UAAAA,QAAQ,CAACQ,MAAT,CAAgB0D,MAAM,CAAC,CAAD,CAAN,CAAU3B,CAA1B,EAA6B2B,MAAM,CAAC,CAAD,CAAN,CAAU1B,CAAvC;;AAEA,eAAK,IAAIQ,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGkB,MAAM,CAACzC,MAA3B,EAAmCuB,CAAC,EAApC,EAAwC;AACpChD,YAAAA,QAAQ,CAACS,MAAT,CAAgByD,MAAM,CAAClB,CAAD,CAAN,CAAUT,CAA1B,EAA6B2B,MAAM,CAAClB,CAAD,CAAN,CAAUR,CAAvC;AACH,WAdsH,CAgBvH;;;AACAxC,UAAAA,QAAQ,CAACS,MAAT,CAAgByD,MAAM,CAAC,CAAD,CAAN,CAAU3B,CAA1B,EAA6B2B,MAAM,CAAC,CAAD,CAAN,CAAU1B,CAAvC;;AAEA,cAAIzB,MAAJ,EAAY;AACRf,YAAAA,QAAQ,CAACkB,IAAT;AACH,WAFD,MAEO;AACHlB,YAAAA,QAAQ,CAACU,MAAT;AACH;AACJ;AAED;AACJ;AACA;;;AACyB,eAAdyD,cAAc,CAACnE,QAAD,EAAqBC,MAArB,EAAqCC,MAArC,EAAqDC,IAArD,EAAmEC,IAAnE,EAAiFC,KAAjF,EAA+F+D,UAAkB,GAAG,CAApH,EAAuHC,SAAiB,GAAG,CAA3I,EAA8I/D,SAAiB,GAAG,CAAlK,EAA2K;AAC5LN,UAAAA,QAAQ,CAACO,WAAT,GAAuBF,KAAvB;AACAL,UAAAA,QAAQ,CAACM,SAAT,GAAqBA,SAArB;AAEA,gBAAMiB,EAAE,GAAGpB,IAAI,GAAGF,MAAlB;AACA,gBAAMuB,EAAE,GAAGpB,IAAI,GAAGF,MAAlB;AACA,gBAAMoE,WAAW,GAAG5C,IAAI,CAACC,IAAL,CAAUJ,EAAE,GAAGA,EAAL,GAAUC,EAAE,GAAGA,EAAzB,CAApB;AACA,gBAAMI,IAAI,GAAGL,EAAE,GAAG+C,WAAlB;AACA,gBAAMzC,IAAI,GAAGL,EAAE,GAAG8C,WAAlB;AAEA,cAAIC,aAAa,GAAG,CAApB;AACA,cAAIC,MAAM,GAAG,IAAb;;AAEA,iBAAOD,aAAa,GAAGD,WAAvB,EAAoC;AAChC,kBAAMG,aAAa,GAAGD,MAAM,GAAGJ,UAAH,GAAgBC,SAA5C;AACA,kBAAMK,UAAU,GAAGhD,IAAI,CAACiD,GAAL,CAASJ,aAAa,GAAGE,aAAzB,EAAwCH,WAAxC,CAAnB;AAEA,kBAAMM,QAAQ,GAAG3E,MAAM,GAAG2B,IAAI,GAAG2C,aAAjC;AACA,kBAAMM,QAAQ,GAAG3E,MAAM,GAAG2B,IAAI,GAAG0C,aAAjC;AACA,kBAAMO,KAAK,GAAG7E,MAAM,GAAG2B,IAAI,GAAG8C,UAA9B;AACA,kBAAMK,KAAK,GAAG7E,MAAM,GAAG2B,IAAI,GAAG6C,UAA9B;;AAEA,gBAAIF,MAAJ,EAAY;AACRxE,cAAAA,QAAQ,CAACQ,MAAT,CAAgBoE,QAAhB,EAA0BC,QAA1B;AACA7E,cAAAA,QAAQ,CAACS,MAAT,CAAgBqE,KAAhB,EAAuBC,KAAvB;AACH;;AAEDR,YAAAA,aAAa,GAAGG,UAAhB;AACAF,YAAAA,MAAM,GAAG,CAACA,MAAV;AACH;;AAEDxE,UAAAA,QAAQ,CAACU,MAAT;AACH;AAED;AACJ;AACA;;;AACmB,eAARsE,QAAQ,CAAChF,QAAD,EAAqBY,OAArB,EAAsCC,OAAtC,EAAuD4B,KAAvD,EAAsEC,MAAtE,EAAsFuC,QAAtF,EAAwG5E,KAAxG,EAAsHC,SAAiB,GAAG,CAA1I,EAAmJ;AAC9JN,UAAAA,QAAQ,CAACO,WAAT,GAAuBF,KAAvB;AACAL,UAAAA,QAAQ,CAACM,SAAT,GAAqBA,SAArB;AAEA,gBAAM4E,SAAS,GAAGzC,KAAK,GAAG,CAA1B;AACA,gBAAM0C,UAAU,GAAGzC,MAAM,GAAG,CAA5B;AACA,gBAAM0C,IAAI,GAAGxE,OAAO,GAAGsE,SAAvB;AACA,gBAAMG,KAAK,GAAGzE,OAAO,GAAGsE,SAAxB;AACA,gBAAMI,GAAG,GAAGzE,OAAO,GAAGsE,UAAtB;AACA,gBAAMI,MAAM,GAAG1E,OAAO,GAAGsE,UAAzB,CAT8J,CAW9J;;AACA,eAAK,IAAI5C,CAAC,GAAG6C,IAAb,EAAmB7C,CAAC,IAAI8C,KAAxB,EAA+B9C,CAAC,IAAI0C,QAApC,EAA8C;AAC1CjF,YAAAA,QAAQ,CAACQ,MAAT,CAAgB+B,CAAhB,EAAmB+C,GAAnB;AACAtF,YAAAA,QAAQ,CAACS,MAAT,CAAgB8B,CAAhB,EAAmBgD,MAAnB;AACH,WAf6J,CAiB9J;;;AACA,eAAK,IAAI/C,CAAC,GAAG8C,GAAb,EAAkB9C,CAAC,IAAI+C,MAAvB,EAA+B/C,CAAC,IAAIyC,QAApC,EAA8C;AAC1CjF,YAAAA,QAAQ,CAACQ,MAAT,CAAgB4E,IAAhB,EAAsB5C,CAAtB;AACAxC,YAAAA,QAAQ,CAACS,MAAT,CAAgB4E,KAAhB,EAAuB7C,CAAvB;AACH;;AAEDxC,UAAAA,QAAQ,CAACU,MAAT;AACH;AAED;AACJ;AACA;;;AACsB,eAAX8E,WAAW,CAACxF,QAAD,EAAqBY,OAArB,EAAsCC,OAAtC,EAAuDO,IAAvD,EAAqEf,KAArE,EAAmFU,MAAe,GAAG,KAArG,EAA4GT,SAAiB,GAAG,CAAhI,EAAyI;AACvJ,gBAAMmF,QAAQ,GAAGrE,IAAI,GAAG,CAAxB;AACA,gBAAM8C,MAAM,GAAG,CACX,IAAIpE,IAAJ,CAASc,OAAT,EAAkBC,OAAO,GAAG4E,QAA5B,CADW,EAC4B;AACvC,cAAI3F,IAAJ,CAASc,OAAO,GAAG6E,QAAnB,EAA6B5E,OAA7B,CAFW,EAE4B;AACvC,cAAIf,IAAJ,CAASc,OAAT,EAAkBC,OAAO,GAAG4E,QAA5B,CAHW,EAG4B;AACvC,cAAI3F,IAAJ,CAASc,OAAO,GAAG6E,QAAnB,EAA6B5E,OAA7B,CAJW,CAI4B;AAJ5B,WAAf;AAOAhB,UAAAA,UAAU,CAACoE,WAAX,CAAuBjE,QAAvB,EAAiCkE,MAAjC,EAAyC7D,KAAzC,EAAgDU,MAAhD,EAAwDT,SAAxD;AACH;AAED;AACJ;AACA;;;AACmB,eAARoF,QAAQ,CAAC1F,QAAD,EAAqBY,OAArB,EAAsCC,OAAtC,EAAuD8E,WAAvD,EAA4EC,WAA5E,EAAiG1B,MAAjG,EAAiH7D,KAAjH,EAA+HU,MAAe,GAAG,KAAjJ,EAAwJT,SAAiB,GAAG,CAA5K,EAAqL;AAChM,gBAAMuF,QAAgB,GAAG,EAAzB;AACA,gBAAM9B,SAAS,GAAIrC,IAAI,CAACK,EAAL,GAAU,CAAX,IAAiBmC,MAAM,GAAG,CAA1B,CAAlB;;AAEA,eAAK,IAAIlB,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGkB,MAAM,GAAG,CAA7B,EAAgClB,CAAC,EAAjC,EAAqC;AACjC,kBAAMgB,KAAK,GAAGhB,CAAC,GAAGe,SAAJ,GAAgBrC,IAAI,CAACK,EAAL,GAAU,CAAxC,CADiC,CACU;;AAC3C,kBAAMjB,MAAM,GAAGkC,CAAC,GAAG,CAAJ,KAAU,CAAV,GAAc2C,WAAd,GAA4BC,WAA3C;AACA,kBAAMrD,CAAC,GAAG3B,OAAO,GAAGc,IAAI,CAACO,GAAL,CAAS+B,KAAT,IAAkBlD,MAAtC;AACA,kBAAM0B,CAAC,GAAG3B,OAAO,GAAGa,IAAI,CAACQ,GAAL,CAAS8B,KAAT,IAAkBlD,MAAtC;AACA+E,YAAAA,QAAQ,CAACC,IAAT,CAAc,IAAIhG,IAAJ,CAASyC,CAAT,EAAYC,CAAZ,CAAd;AACH;;AAED3C,UAAAA,UAAU,CAACoE,WAAX,CAAuBjE,QAAvB,EAAiC6F,QAAjC,EAA2CxF,KAA3C,EAAkDU,MAAlD,EAA0DT,SAA1D;AACH;AAED;AACJ;AACA;;;AAC6B,eAAlByF,kBAAkB,CAAC/F,QAAD,EAAqBY,OAArB,EAAsCC,OAAtC,EAAuDC,MAAvD,EAAuEkF,KAAvE,EAAsF3F,KAAtF,EAAoGU,MAAe,GAAG,KAAtH,EAA6HT,SAAiB,GAAG,CAAjJ,EAA0J;AAC/K,cAAI0F,KAAK,GAAG,CAAZ,EAAe;AAEf,gBAAMH,QAAgB,GAAG,EAAzB;AACA,gBAAM9B,SAAS,GAAIrC,IAAI,CAACK,EAAL,GAAU,CAAX,GAAgBiE,KAAlC;;AAEA,eAAK,IAAIhD,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGgD,KAApB,EAA2BhD,CAAC,EAA5B,EAAgC;AAC5B,kBAAMgB,KAAK,GAAGhB,CAAC,GAAGe,SAAJ,GAAgBrC,IAAI,CAACK,EAAL,GAAU,CAAxC,CAD4B,CACe;;AAC3C,kBAAMQ,CAAC,GAAG3B,OAAO,GAAGc,IAAI,CAACO,GAAL,CAAS+B,KAAT,IAAkBlD,MAAtC;AACA,kBAAM0B,CAAC,GAAG3B,OAAO,GAAGa,IAAI,CAACQ,GAAL,CAAS8B,KAAT,IAAkBlD,MAAtC;AACA+E,YAAAA,QAAQ,CAACC,IAAT,CAAc,IAAIhG,IAAJ,CAASyC,CAAT,EAAYC,CAAZ,CAAd;AACH;;AAED3C,UAAAA,UAAU,CAACoE,WAAX,CAAuBjE,QAAvB,EAAiC6F,QAAjC,EAA2CxF,KAA3C,EAAkDU,MAAlD,EAA0DT,SAA1D;AACH;AAED;AACJ;AACA;;;AACqB,eAAV2F,UAAU,CAACjG,QAAD,EAAqBY,OAArB,EAAsCC,OAAtC,EAAuDqF,WAAvD,EAA4EC,SAA5E,EAA+FC,KAA/F,EAA8G/F,KAA9G,EAA4H0C,QAAgB,GAAG,GAA/I,EAAoJzC,SAAiB,GAAG,CAAxK,EAAiL;AAC9LN,UAAAA,QAAQ,CAACO,WAAT,GAAuBF,KAAvB;AACAL,UAAAA,QAAQ,CAACM,SAAT,GAAqBA,SAArB;AAEA,gBAAM+F,UAAU,GAAGD,KAAK,GAAG1E,IAAI,CAACK,EAAb,GAAkB,CAArC;AACA,gBAAMuE,UAAU,GAAG,CAACH,SAAS,GAAGD,WAAb,IAA4BnD,QAA/C;AACA,gBAAMgB,SAAS,GAAGsC,UAAU,GAAGtD,QAA/B;;AAEA,eAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,IAAID,QAArB,EAA+BC,CAAC,EAAhC,EAAoC;AAChC,kBAAMgB,KAAK,GAAGhB,CAAC,GAAGe,SAAlB;AACA,kBAAMjD,MAAM,GAAGoF,WAAW,GAAGlD,CAAC,GAAGsD,UAAjC;AACA,kBAAM/D,CAAC,GAAG3B,OAAO,GAAGc,IAAI,CAACO,GAAL,CAAS+B,KAAT,IAAkBlD,MAAtC;AACA,kBAAM0B,CAAC,GAAG3B,OAAO,GAAGa,IAAI,CAACQ,GAAL,CAAS8B,KAAT,IAAkBlD,MAAtC;;AAEA,gBAAIkC,CAAC,KAAK,CAAV,EAAa;AACThD,cAAAA,QAAQ,CAACQ,MAAT,CAAgB+B,CAAhB,EAAmBC,CAAnB;AACH,aAFD,MAEO;AACHxC,cAAAA,QAAQ,CAACS,MAAT,CAAgB8B,CAAhB,EAAmBC,CAAnB;AACH;AACJ;;AAEDxC,UAAAA,QAAQ,CAACU,MAAT;AACH;AAED;AACJ;AACA;;;AAC+B,eAApB6F,oBAAoB,CAACvG,QAAD,EAAqBY,OAArB,EAAsCC,OAAtC,EAAuD2F,UAAvD,EAA2EnG,KAA3E,EAAyFC,SAAiB,GAAG,CAA7G,EAAgHmG,UAAmB,GAAG,IAAtI,EAAkJ;AACzK;AACA5G,UAAAA,UAAU,CAACE,QAAX,CAAoBC,QAApB,EAA8BY,OAAO,GAAG4F,UAAxC,EAAoD3F,OAApD,EAA6DD,OAAO,GAAG4F,UAAvE,EAAmF3F,OAAnF,EAA4FR,KAA5F,EAAmGC,SAAnG,EAFyK,CAIzK;;AACAT,UAAAA,UAAU,CAACE,QAAX,CAAoBC,QAApB,EAA8BY,OAA9B,EAAuCC,OAAO,GAAG2F,UAAjD,EAA6D5F,OAA7D,EAAsEC,OAAO,GAAG2F,UAAhF,EAA4FnG,KAA5F,EAAmGC,SAAnG;;AAEA,cAAImG,UAAJ,EAAgB;AACZ,kBAAMnF,SAAS,GAAGI,IAAI,CAACiD,GAAL,CAAS6B,UAAU,GAAG,GAAtB,EAA2B,EAA3B,CAAlB,CADY,CAGZ;;AACA3G,YAAAA,UAAU,CAACwB,SAAX,CAAqBrB,QAArB,EAA+BY,OAAO,GAAG4F,UAAV,GAAuBlF,SAAtD,EAAiET,OAAjE,EAA0ED,OAAO,GAAG4F,UAApF,EAAgG3F,OAAhG,EAAyGR,KAAzG,EAAgHiB,SAAhH,EAA2HhB,SAA3H,EAJY,CAMZ;;AACAT,YAAAA,UAAU,CAACwB,SAAX,CAAqBrB,QAArB,EAA+BY,OAA/B,EAAwCC,OAAO,GAAG2F,UAAV,GAAuBlF,SAA/D,EAA0EV,OAA1E,EAAmFC,OAAO,GAAG2F,UAA7F,EAAyGnG,KAAzG,EAAgHiB,SAAhH,EAA2HhB,SAA3H;AACH;AACJ;;AA/WmB,O", "sourcesContent": ["import { _decorator, Graphics, Color, Vec2, Vec3 } from 'cc';\n\n/**\n * Utility class for common 2D gizmo drawing functions\n * Provides a comprehensive set of drawing primitives for gizmo visualization\n */\nexport class GizmoUtils {\n    \n    /**\n     * Draw a simple line between two points\n     */\n    static drawLine(graphics: Graphics, startX: number, startY: number, endX: number, endY: number, color: Color, lineWidth: number = 1): void {\n        graphics.strokeColor = color;\n        graphics.lineWidth = lineWidth;\n        \n        graphics.moveTo(startX, startY);\n        graphics.lineTo(endX, endY);\n        graphics.stroke();\n    }\n    \n    /**\n     * Draw a circle (filled or outline)\n     */\n    static drawCircle(graphics: Graphics, centerX: number, centerY: number, radius: number, color: Color, filled: boolean = false, lineWidth: number = 1): void {\n        if (filled) {\n            graphics.fillColor = color;\n            graphics.circle(centerX, centerY, radius);\n            graphics.fill();\n        } else {\n            graphics.strokeColor = color;\n            graphics.lineWidth = lineWidth;\n            graphics.circle(centerX, centerY, radius);\n            graphics.stroke();\n        }\n    }\n    \n    /**\n     * Draw a cross/plus symbol at the specified position\n     */\n    static drawCross(graphics: Graphics, centerX: number, centerY: number, size: number, color: Color, lineWidth: number = 2): void {\n        graphics.strokeColor = color;\n        graphics.lineWidth = lineWidth;\n        \n        // Horizontal line\n        graphics.moveTo(centerX - size, centerY);\n        graphics.lineTo(centerX + size, centerY);\n        \n        // Vertical line\n        graphics.moveTo(centerX, centerY - size);\n        graphics.lineTo(centerX, centerY + size);\n        \n        graphics.stroke();\n    }\n    \n    /**\n     * Draw an arrow from start to end point\n     */\n    static drawArrow(graphics: Graphics, startX: number, startY: number, endX: number, endY: number, color: Color, arrowSize: number = 8, lineWidth: number = 2): void {\n        graphics.strokeColor = color;\n        graphics.lineWidth = lineWidth;\n        \n        // Draw main line\n        graphics.moveTo(startX, startY);\n        graphics.lineTo(endX, endY);\n        \n        // Calculate arrow head\n        const dx = endX - startX;\n        const dy = endY - startY;\n        const length = Math.sqrt(dx * dx + dy * dy);\n        \n        if (length > 0) {\n            const dirX = dx / length;\n            const dirY = dy / length;\n            \n            // Arrow head angle (30 degrees)\n            const arrowAngle = Math.PI / 6;\n            \n            // Left arrow point\n            const leftX = endX - arrowSize * (dirX * Math.cos(arrowAngle) - dirY * Math.sin(arrowAngle));\n            const leftY = endY - arrowSize * (dirY * Math.cos(arrowAngle) + dirX * Math.sin(arrowAngle));\n            \n            // Right arrow point\n            const rightX = endX - arrowSize * (dirX * Math.cos(-arrowAngle) - dirY * Math.sin(-arrowAngle));\n            const rightY = endY - arrowSize * (dirY * Math.cos(-arrowAngle) + dirX * Math.sin(-arrowAngle));\n            \n            // Draw arrow head\n            graphics.moveTo(endX, endY);\n            graphics.lineTo(leftX, leftY);\n            graphics.moveTo(endX, endY);\n            graphics.lineTo(rightX, rightY);\n        }\n        \n        graphics.stroke();\n    }\n    \n    /**\n     * Draw a rectangle (filled or outline)\n     */\n    static drawRect(graphics: Graphics, x: number, y: number, width: number, height: number, color: Color, filled: boolean = false, lineWidth: number = 1): void {\n        if (filled) {\n            graphics.fillColor = color;\n            graphics.rect(x, y, width, height);\n            graphics.fill();\n        } else {\n            graphics.strokeColor = color;\n            graphics.lineWidth = lineWidth;\n            graphics.rect(x, y, width, height);\n            graphics.stroke();\n        }\n    }\n    \n    /**\n     * Draw a quadratic Bezier curve\n     */\n    static drawQuadraticBezier(graphics: Graphics, startX: number, startY: number, controlX: number, controlY: number, endX: number, endY: number, color: Color, segments: number = 20, lineWidth: number = 1): void {\n        graphics.strokeColor = color;\n        graphics.lineWidth = lineWidth;\n        \n        graphics.moveTo(startX, startY);\n        \n        for (let i = 1; i <= segments; i++) {\n            const t = i / segments;\n            const x = (1 - t) * (1 - t) * startX + 2 * (1 - t) * t * controlX + t * t * endX;\n            const y = (1 - t) * (1 - t) * startY + 2 * (1 - t) * t * controlY + t * t * endY;\n            graphics.lineTo(x, y);\n        }\n        \n        graphics.stroke();\n    }\n    \n    /**\n     * Draw a cubic Bezier curve\n     */\n    static drawCubicBezier(graphics: Graphics, startX: number, startY: number, control1X: number, control1Y: number, control2X: number, control2Y: number, endX: number, endY: number, color: Color, segments: number = 30, lineWidth: number = 1): void {\n        graphics.strokeColor = color;\n        graphics.lineWidth = lineWidth;\n        \n        graphics.moveTo(startX, startY);\n        \n        for (let i = 1; i <= segments; i++) {\n            const t = i / segments;\n            const t2 = t * t;\n            const t3 = t2 * t;\n            const mt = 1 - t;\n            const mt2 = mt * mt;\n            const mt3 = mt2 * mt;\n            \n            const x = mt3 * startX + 3 * mt2 * t * control1X + 3 * mt * t2 * control2X + t3 * endX;\n            const y = mt3 * startY + 3 * mt2 * t * control1Y + 3 * mt * t2 * control2Y + t3 * endY;\n            \n            graphics.lineTo(x, y);\n        }\n        \n        graphics.stroke();\n    }\n    \n    /**\n     * Draw an arc (portion of a circle)\n     */\n    static drawArc(graphics: Graphics, centerX: number, centerY: number, radius: number, startAngle: number, endAngle: number, color: Color, segments: number = 20, lineWidth: number = 1): void {\n        graphics.strokeColor = color;\n        graphics.lineWidth = lineWidth;\n        \n        const angleStep = (endAngle - startAngle) / segments;\n        \n        for (let i = 0; i <= segments; i++) {\n            const angle = startAngle + angleStep * i;\n            const x = centerX + Math.cos(angle) * radius;\n            const y = centerY + Math.sin(angle) * radius;\n            \n            if (i === 0) {\n                graphics.moveTo(x, y);\n            } else {\n                graphics.lineTo(x, y);\n            }\n        }\n        \n        graphics.stroke();\n    }\n    \n    /**\n     * Draw a polygon from an array of points\n     */\n    static drawPolygon(graphics: Graphics, points: Vec2[], color: Color, filled: boolean = false, lineWidth: number = 1): void {\n        if (points.length < 2) return;\n        \n        if (filled) {\n            graphics.fillColor = color;\n        } else {\n            graphics.strokeColor = color;\n            graphics.lineWidth = lineWidth;\n        }\n        \n        graphics.moveTo(points[0].x, points[0].y);\n        \n        for (let i = 1; i < points.length; i++) {\n            graphics.lineTo(points[i].x, points[i].y);\n        }\n        \n        // Close the polygon\n        graphics.lineTo(points[0].x, points[0].y);\n        \n        if (filled) {\n            graphics.fill();\n        } else {\n            graphics.stroke();\n        }\n    }\n    \n    /**\n     * Draw a dashed line\n     */\n    static drawDashedLine(graphics: Graphics, startX: number, startY: number, endX: number, endY: number, color: Color, dashLength: number = 5, gapLength: number = 3, lineWidth: number = 1): void {\n        graphics.strokeColor = color;\n        graphics.lineWidth = lineWidth;\n        \n        const dx = endX - startX;\n        const dy = endY - startY;\n        const totalLength = Math.sqrt(dx * dx + dy * dy);\n        const dirX = dx / totalLength;\n        const dirY = dy / totalLength;\n        \n        let currentLength = 0;\n        let isDash = true;\n        \n        while (currentLength < totalLength) {\n            const segmentLength = isDash ? dashLength : gapLength;\n            const nextLength = Math.min(currentLength + segmentLength, totalLength);\n            \n            const currentX = startX + dirX * currentLength;\n            const currentY = startY + dirY * currentLength;\n            const nextX = startX + dirX * nextLength;\n            const nextY = startY + dirY * nextLength;\n            \n            if (isDash) {\n                graphics.moveTo(currentX, currentY);\n                graphics.lineTo(nextX, nextY);\n            }\n            \n            currentLength = nextLength;\n            isDash = !isDash;\n        }\n        \n        graphics.stroke();\n    }\n    \n    /**\n     * Draw a grid\n     */\n    static drawGrid(graphics: Graphics, centerX: number, centerY: number, width: number, height: number, cellSize: number, color: Color, lineWidth: number = 1): void {\n        graphics.strokeColor = color;\n        graphics.lineWidth = lineWidth;\n        \n        const halfWidth = width / 2;\n        const halfHeight = height / 2;\n        const left = centerX - halfWidth;\n        const right = centerX + halfWidth;\n        const top = centerY - halfHeight;\n        const bottom = centerY + halfHeight;\n        \n        // Vertical lines\n        for (let x = left; x <= right; x += cellSize) {\n            graphics.moveTo(x, top);\n            graphics.lineTo(x, bottom);\n        }\n        \n        // Horizontal lines\n        for (let y = top; y <= bottom; y += cellSize) {\n            graphics.moveTo(left, y);\n            graphics.lineTo(right, y);\n        }\n        \n        graphics.stroke();\n    }\n\n    /**\n     * Draw a diamond/rhombus shape\n     */\n    static drawDiamond(graphics: Graphics, centerX: number, centerY: number, size: number, color: Color, filled: boolean = false, lineWidth: number = 1): void {\n        const halfSize = size / 2;\n        const points = [\n            new Vec2(centerX, centerY - halfSize), // Top\n            new Vec2(centerX + halfSize, centerY), // Right\n            new Vec2(centerX, centerY + halfSize), // Bottom\n            new Vec2(centerX - halfSize, centerY)  // Left\n        ];\n\n        GizmoUtils.drawPolygon(graphics, points, color, filled, lineWidth);\n    }\n\n    /**\n     * Draw a star shape\n     */\n    static drawStar(graphics: Graphics, centerX: number, centerY: number, outerRadius: number, innerRadius: number, points: number, color: Color, filled: boolean = false, lineWidth: number = 1): void {\n        const vertices: Vec2[] = [];\n        const angleStep = (Math.PI * 2) / (points * 2);\n\n        for (let i = 0; i < points * 2; i++) {\n            const angle = i * angleStep - Math.PI / 2; // Start from top\n            const radius = i % 2 === 0 ? outerRadius : innerRadius;\n            const x = centerX + Math.cos(angle) * radius;\n            const y = centerY + Math.sin(angle) * radius;\n            vertices.push(new Vec2(x, y));\n        }\n\n        GizmoUtils.drawPolygon(graphics, vertices, color, filled, lineWidth);\n    }\n\n    /**\n     * Draw a regular polygon (triangle, pentagon, hexagon, etc.)\n     */\n    static drawRegularPolygon(graphics: Graphics, centerX: number, centerY: number, radius: number, sides: number, color: Color, filled: boolean = false, lineWidth: number = 1): void {\n        if (sides < 3) return;\n\n        const vertices: Vec2[] = [];\n        const angleStep = (Math.PI * 2) / sides;\n\n        for (let i = 0; i < sides; i++) {\n            const angle = i * angleStep - Math.PI / 2; // Start from top\n            const x = centerX + Math.cos(angle) * radius;\n            const y = centerY + Math.sin(angle) * radius;\n            vertices.push(new Vec2(x, y));\n        }\n\n        GizmoUtils.drawPolygon(graphics, vertices, color, filled, lineWidth);\n    }\n\n    /**\n     * Draw a spiral\n     */\n    static drawSpiral(graphics: Graphics, centerX: number, centerY: number, startRadius: number, endRadius: number, turns: number, color: Color, segments: number = 100, lineWidth: number = 1): void {\n        graphics.strokeColor = color;\n        graphics.lineWidth = lineWidth;\n\n        const totalAngle = turns * Math.PI * 2;\n        const radiusStep = (endRadius - startRadius) / segments;\n        const angleStep = totalAngle / segments;\n\n        for (let i = 0; i <= segments; i++) {\n            const angle = i * angleStep;\n            const radius = startRadius + i * radiusStep;\n            const x = centerX + Math.cos(angle) * radius;\n            const y = centerY + Math.sin(angle) * radius;\n\n            if (i === 0) {\n                graphics.moveTo(x, y);\n            } else {\n                graphics.lineTo(x, y);\n            }\n        }\n\n        graphics.stroke();\n    }\n\n    /**\n     * Draw a coordinate system (X and Y axes)\n     */\n    static drawCoordinateSystem(graphics: Graphics, centerX: number, centerY: number, axisLength: number, color: Color, lineWidth: number = 1, showArrows: boolean = true): void {\n        // X axis (horizontal)\n        GizmoUtils.drawLine(graphics, centerX - axisLength, centerY, centerX + axisLength, centerY, color, lineWidth);\n\n        // Y axis (vertical)\n        GizmoUtils.drawLine(graphics, centerX, centerY - axisLength, centerX, centerY + axisLength, color, lineWidth);\n\n        if (showArrows) {\n            const arrowSize = Math.min(axisLength * 0.1, 10);\n\n            // X axis arrow (pointing right)\n            GizmoUtils.drawArrow(graphics, centerX + axisLength - arrowSize, centerY, centerX + axisLength, centerY, color, arrowSize, lineWidth);\n\n            // Y axis arrow (pointing up)\n            GizmoUtils.drawArrow(graphics, centerX, centerY - axisLength + arrowSize, centerX, centerY - axisLength, color, arrowSize, lineWidth);\n        }\n    }\n}\n"]}