import { _decorator, Graphics, Color, Vec2, Vec3 } from 'cc';

/**
 * Utility class for common 2D gizmo drawing functions
 * Provides a comprehensive set of drawing primitives for gizmo visualization
 */
export class GizmoUtils {
    
    /**
     * Draw a simple line between two points
     */
    static drawLine(graphics: Graphics, startX: number, startY: number, endX: number, endY: number, color: Color, lineWidth: number = 1): void {
        graphics.strokeColor = color;
        graphics.lineWidth = lineWidth;
        
        graphics.moveTo(startX, startY);
        graphics.lineTo(endX, endY);
        graphics.stroke();
    }
    
    /**
     * Draw a circle (filled or outline)
     */
    static drawCircle(graphics: Graphics, centerX: number, centerY: number, radius: number, color: Color, filled: boolean = false, lineWidth: number = 1): void {
        if (filled) {
            graphics.fillColor = color;
            graphics.circle(centerX, centerY, radius);
            graphics.fill();
        } else {
            graphics.strokeColor = color;
            graphics.lineWidth = lineWidth;
            graphics.circle(centerX, centerY, radius);
            graphics.stroke();
        }
    }
    
    /**
     * Draw a cross/plus symbol at the specified position
     */
    static drawCross(graphics: Graphics, centerX: number, centerY: number, size: number, color: Color, lineWidth: number = 2): void {
        graphics.strokeColor = color;
        graphics.lineWidth = lineWidth;
        
        // Horizontal line
        graphics.moveTo(centerX - size, centerY);
        graphics.lineTo(centerX + size, centerY);
        
        // Vertical line
        graphics.moveTo(centerX, centerY - size);
        graphics.lineTo(centerX, centerY + size);
        
        graphics.stroke();
    }
    
    /**
     * Draw an arrow from start to end point
     */
    static drawArrow(graphics: Graphics, startX: number, startY: number, endX: number, endY: number, color: Color, arrowSize: number = 8, lineWidth: number = 2): void {
        graphics.strokeColor = color;
        graphics.lineWidth = lineWidth;
        
        // Draw main line
        graphics.moveTo(startX, startY);
        graphics.lineTo(endX, endY);
        
        // Calculate arrow head
        const dx = endX - startX;
        const dy = endY - startY;
        const length = Math.sqrt(dx * dx + dy * dy);
        
        if (length > 0) {
            const dirX = dx / length;
            const dirY = dy / length;
            
            // Arrow head angle (30 degrees)
            const arrowAngle = Math.PI / 6;
            
            // Left arrow point
            const leftX = endX - arrowSize * (dirX * Math.cos(arrowAngle) - dirY * Math.sin(arrowAngle));
            const leftY = endY - arrowSize * (dirY * Math.cos(arrowAngle) + dirX * Math.sin(arrowAngle));
            
            // Right arrow point
            const rightX = endX - arrowSize * (dirX * Math.cos(-arrowAngle) - dirY * Math.sin(-arrowAngle));
            const rightY = endY - arrowSize * (dirY * Math.cos(-arrowAngle) + dirX * Math.sin(-arrowAngle));
            
            // Draw arrow head
            graphics.moveTo(endX, endY);
            graphics.lineTo(leftX, leftY);
            graphics.moveTo(endX, endY);
            graphics.lineTo(rightX, rightY);
        }
        
        graphics.stroke();
    }
    
    /**
     * Draw a rectangle (filled or outline)
     */
    static drawRect(graphics: Graphics, x: number, y: number, width: number, height: number, color: Color, filled: boolean = false, lineWidth: number = 1): void {
        if (filled) {
            graphics.fillColor = color;
            graphics.rect(x, y, width, height);
            graphics.fill();
        } else {
            graphics.strokeColor = color;
            graphics.lineWidth = lineWidth;
            graphics.rect(x, y, width, height);
            graphics.stroke();
        }
    }
    
    /**
     * Draw a quadratic Bezier curve
     */
    static drawQuadraticBezier(graphics: Graphics, startX: number, startY: number, controlX: number, controlY: number, endX: number, endY: number, color: Color, segments: number = 20, lineWidth: number = 1): void {
        graphics.strokeColor = color;
        graphics.lineWidth = lineWidth;
        
        graphics.moveTo(startX, startY);
        
        for (let i = 1; i <= segments; i++) {
            const t = i / segments;
            const x = (1 - t) * (1 - t) * startX + 2 * (1 - t) * t * controlX + t * t * endX;
            const y = (1 - t) * (1 - t) * startY + 2 * (1 - t) * t * controlY + t * t * endY;
            graphics.lineTo(x, y);
        }
        
        graphics.stroke();
    }
    
    /**
     * Draw a cubic Bezier curve
     */
    static drawCubicBezier(graphics: Graphics, startX: number, startY: number, control1X: number, control1Y: number, control2X: number, control2Y: number, endX: number, endY: number, color: Color, segments: number = 30, lineWidth: number = 1): void {
        graphics.strokeColor = color;
        graphics.lineWidth = lineWidth;
        
        graphics.moveTo(startX, startY);
        
        for (let i = 1; i <= segments; i++) {
            const t = i / segments;
            const t2 = t * t;
            const t3 = t2 * t;
            const mt = 1 - t;
            const mt2 = mt * mt;
            const mt3 = mt2 * mt;
            
            const x = mt3 * startX + 3 * mt2 * t * control1X + 3 * mt * t2 * control2X + t3 * endX;
            const y = mt3 * startY + 3 * mt2 * t * control1Y + 3 * mt * t2 * control2Y + t3 * endY;
            
            graphics.lineTo(x, y);
        }
        
        graphics.stroke();
    }
    
    /**
     * Draw an arc (portion of a circle)
     */
    static drawArc(graphics: Graphics, centerX: number, centerY: number, radius: number, startAngle: number, endAngle: number, color: Color, segments: number = 20, lineWidth: number = 1): void {
        graphics.strokeColor = color;
        graphics.lineWidth = lineWidth;
        
        const angleStep = (endAngle - startAngle) / segments;
        
        for (let i = 0; i <= segments; i++) {
            const angle = startAngle + angleStep * i;
            const x = centerX + Math.cos(angle) * radius;
            const y = centerY + Math.sin(angle) * radius;
            
            if (i === 0) {
                graphics.moveTo(x, y);
            } else {
                graphics.lineTo(x, y);
            }
        }
        
        graphics.stroke();
    }
    
    /**
     * Draw a polygon from an array of points
     */
    static drawPolygon(graphics: Graphics, points: Vec2[], color: Color, filled: boolean = false, lineWidth: number = 1): void {
        if (points.length < 2) return;
        
        if (filled) {
            graphics.fillColor = color;
        } else {
            graphics.strokeColor = color;
            graphics.lineWidth = lineWidth;
        }
        
        graphics.moveTo(points[0].x, points[0].y);
        
        for (let i = 1; i < points.length; i++) {
            graphics.lineTo(points[i].x, points[i].y);
        }
        
        // Close the polygon
        graphics.lineTo(points[0].x, points[0].y);
        
        if (filled) {
            graphics.fill();
        } else {
            graphics.stroke();
        }
    }
    
    /**
     * Draw a dashed line
     */
    static drawDashedLine(graphics: Graphics, startX: number, startY: number, endX: number, endY: number, color: Color, dashLength: number = 5, gapLength: number = 3, lineWidth: number = 1): void {
        graphics.strokeColor = color;
        graphics.lineWidth = lineWidth;
        
        const dx = endX - startX;
        const dy = endY - startY;
        const totalLength = Math.sqrt(dx * dx + dy * dy);
        const dirX = dx / totalLength;
        const dirY = dy / totalLength;
        
        let currentLength = 0;
        let isDash = true;
        
        while (currentLength < totalLength) {
            const segmentLength = isDash ? dashLength : gapLength;
            const nextLength = Math.min(currentLength + segmentLength, totalLength);
            
            const currentX = startX + dirX * currentLength;
            const currentY = startY + dirY * currentLength;
            const nextX = startX + dirX * nextLength;
            const nextY = startY + dirY * nextLength;
            
            if (isDash) {
                graphics.moveTo(currentX, currentY);
                graphics.lineTo(nextX, nextY);
            }
            
            currentLength = nextLength;
            isDash = !isDash;
        }
        
        graphics.stroke();
    }
    
    /**
     * Draw a grid
     */
    static drawGrid(graphics: Graphics, centerX: number, centerY: number, width: number, height: number, cellSize: number, color: Color, lineWidth: number = 1): void {
        graphics.strokeColor = color;
        graphics.lineWidth = lineWidth;
        
        const halfWidth = width / 2;
        const halfHeight = height / 2;
        const left = centerX - halfWidth;
        const right = centerX + halfWidth;
        const top = centerY - halfHeight;
        const bottom = centerY + halfHeight;
        
        // Vertical lines
        for (let x = left; x <= right; x += cellSize) {
            graphics.moveTo(x, top);
            graphics.lineTo(x, bottom);
        }
        
        // Horizontal lines
        for (let y = top; y <= bottom; y += cellSize) {
            graphics.moveTo(left, y);
            graphics.lineTo(right, y);
        }
        
        graphics.stroke();
    }

    /**
     * Draw a diamond/rhombus shape
     */
    static drawDiamond(graphics: Graphics, centerX: number, centerY: number, size: number, color: Color, filled: boolean = false, lineWidth: number = 1): void {
        const halfSize = size / 2;
        const points = [
            new Vec2(centerX, centerY - halfSize), // Top
            new Vec2(centerX + halfSize, centerY), // Right
            new Vec2(centerX, centerY + halfSize), // Bottom
            new Vec2(centerX - halfSize, centerY)  // Left
        ];

        GizmoUtils.drawPolygon(graphics, points, color, filled, lineWidth);
    }

    /**
     * Draw a star shape
     */
    static drawStar(graphics: Graphics, centerX: number, centerY: number, outerRadius: number, innerRadius: number, points: number, color: Color, filled: boolean = false, lineWidth: number = 1): void {
        const vertices: Vec2[] = [];
        const angleStep = (Math.PI * 2) / (points * 2);

        for (let i = 0; i < points * 2; i++) {
            const angle = i * angleStep - Math.PI / 2; // Start from top
            const radius = i % 2 === 0 ? outerRadius : innerRadius;
            const x = centerX + Math.cos(angle) * radius;
            const y = centerY + Math.sin(angle) * radius;
            vertices.push(new Vec2(x, y));
        }

        GizmoUtils.drawPolygon(graphics, vertices, color, filled, lineWidth);
    }

    /**
     * Draw a regular polygon (triangle, pentagon, hexagon, etc.)
     */
    static drawRegularPolygon(graphics: Graphics, centerX: number, centerY: number, radius: number, sides: number, color: Color, filled: boolean = false, lineWidth: number = 1): void {
        if (sides < 3) return;

        const vertices: Vec2[] = [];
        const angleStep = (Math.PI * 2) / sides;

        for (let i = 0; i < sides; i++) {
            const angle = i * angleStep - Math.PI / 2; // Start from top
            const x = centerX + Math.cos(angle) * radius;
            const y = centerY + Math.sin(angle) * radius;
            vertices.push(new Vec2(x, y));
        }

        GizmoUtils.drawPolygon(graphics, vertices, color, filled, lineWidth);
    }

    /**
     * Draw a spiral
     */
    static drawSpiral(graphics: Graphics, centerX: number, centerY: number, startRadius: number, endRadius: number, turns: number, color: Color, segments: number = 100, lineWidth: number = 1): void {
        graphics.strokeColor = color;
        graphics.lineWidth = lineWidth;

        const totalAngle = turns * Math.PI * 2;
        const radiusStep = (endRadius - startRadius) / segments;
        const angleStep = totalAngle / segments;

        for (let i = 0; i <= segments; i++) {
            const angle = i * angleStep;
            const radius = startRadius + i * radiusStep;
            const x = centerX + Math.cos(angle) * radius;
            const y = centerY + Math.sin(angle) * radius;

            if (i === 0) {
                graphics.moveTo(x, y);
            } else {
                graphics.lineTo(x, y);
            }
        }

        graphics.stroke();
    }

    /**
     * Draw a coordinate system (X and Y axes)
     */
    static drawCoordinateSystem(graphics: Graphics, centerX: number, centerY: number, axisLength: number, color: Color, lineWidth: number = 1, showArrows: boolean = true): void {
        // X axis (horizontal)
        GizmoUtils.drawLine(graphics, centerX - axisLength, centerY, centerX + axisLength, centerY, color, lineWidth);

        // Y axis (vertical)
        GizmoUtils.drawLine(graphics, centerX, centerY - axisLength, centerX, centerY + axisLength, color, lineWidth);

        if (showArrows) {
            const arrowSize = Math.min(axisLength * 0.1, 10);

            // X axis arrow (pointing right)
            GizmoUtils.drawArrow(graphics, centerX + axisLength - arrowSize, centerY, centerX + axisLength, centerY, color, arrowSize, lineWidth);

            // Y axis arrow (pointing up)
            GizmoUtils.drawArrow(graphics, centerX, centerY - axisLength + arrowSize, centerX, centerY - axisLength, color, arrowSize, lineWidth);
        }
    }
}
