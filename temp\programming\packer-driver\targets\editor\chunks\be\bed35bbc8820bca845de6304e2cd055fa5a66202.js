System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, GizmoManager, _crd, GIZMO_SYSTEM_VERSION;

  /**
   * Setup the default gizmo system with auto-registered drawers
   * Call this once when your game starts or in the editor
   * Note: The gizmo classes are already imported above, so their decorators have been executed
   */
  function setupDefaultGizmos() {
    // Auto-register all decorated gizmo drawers
    (_crd && GizmoManager === void 0 ? (_reportPossibleCrUseOfGizmoManager({
      error: Error()
    }), GizmoManager) : GizmoManager).autoRegisterDrawers();
    console.log('Gizmo System: Auto-registered gizmos');
  }
  /**
   * Create a gizmo manager node and attach it to the scene
   * @param sceneName Optional scene name, if not provided uses current scene
   * @returns The created gizmo manager node
   */


  function createGizmoManagerNode(sceneName) {
    const {
      Node,
      find,
      director
    } = require('cc');

    let scene = null;

    if (sceneName) {
      scene = find(sceneName);
    } else {
      scene = director.getScene();
    }

    if (!scene) {
      console.error('GizmoSystem: Could not find scene to attach gizmo manager');
      return null;
    } // Create gizmo manager node


    const gizmoNode = new Node('GizmoManager');
    gizmoNode.addComponent(_crd && GizmoManager === void 0 ? (_reportPossibleCrUseOfGizmoManager({
      error: Error()
    }), GizmoManager) : GizmoManager); // Add to scene

    scene.addChild(gizmoNode);
    console.log('GizmoSystem: Created gizmo manager node');
    return gizmoNode;
  }
  /**
   * Quick setup function that creates the gizmo manager and registers default drawers
   * @param sceneName Optional scene name
   * @returns The created gizmo manager node
   */


  function quickSetupGizmos(sceneName) {
    const gizmoNode = createGizmoManagerNode(sceneName);

    if (gizmoNode) {
      setupDefaultGizmos();
    }

    return gizmoNode;
  }
  /**
   * Version information for the Gizmo system
   */


  function _reportPossibleCrUseOfGizmoManager(extras) {
    _reporterNs.report("GizmoManager", "./GizmoManager", _context.meta, extras);
  }

  _export({
    setupDefaultGizmos: setupDefaultGizmos,
    createGizmoManagerNode: createGizmoManagerNode,
    quickSetupGizmos: quickSetupGizmos
  });

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
    }, function (_unresolved_2) {
      _export({
        GizmoDrawer: _unresolved_2.GizmoDrawer,
        RegisterGizmoDrawer: _unresolved_2.RegisterGizmoDrawer,
        autoRegisterGizmoDrawers: _unresolved_2.autoRegisterGizmoDrawers,
        getRegisteredGizmoDrawers: _unresolved_2.getRegisteredGizmoDrawers
      });
    }, function (_unresolved_3) {
      _export("GizmoManager", _unresolved_3.GizmoManager);
    }, function (_unresolved_4) {
      _export("GizmoUtils", _unresolved_4.GizmoUtils);
    }, function (_unresolved_5) {
      _export("EmitterArcGizmo", _unresolved_5.EmitterArcGizmo);
    }, function (_unresolved_6) {
      GizmoManager = _unresolved_6.GizmoManager;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "29b38aYkT9DV5qK3eizg4U1", "index", undefined);
      /**
       * Gizmo System Index
       * 
       * This file exports all components of the Gizmo system for easy importing
       * and provides utilities for setting up the gizmo system.
       */
      // Core gizmo system
      // Specific gizmo drawers (importing these triggers the @RegisterGizmoDrawer decorators)
      // Setup utilities


      _export("GIZMO_SYSTEM_VERSION", GIZMO_SYSTEM_VERSION = "1.0.0");
      /**
       * Example usage documentation
       * 
       * Basic setup in your game initialization:
       * ```typescript
       * import { quickSetupGizmos } from "./Game/gizmos";
       * 
       * // In your game startup code
       * quickSetupGizmos();
       * ```
       * 
       * Manual setup:
       * ```typescript
       * import {
       *     GizmoManager,
       *     EmitterArcGizmo,
       *     setupDefaultGizmos
       * } from "./Game/gizmos";
       * 
       * // Create gizmo manager node manually
       * const gizmoNode = new Node('GizmoManager');
       * gizmoNode.addComponent(GizmoManager);
       * scene.addChild(gizmoNode);
       * 
       * // Register default drawers
       * setupDefaultGizmos();
       * 
       * // Or register custom drawers
       * const customEmitterGizmo = new EmitterArcGizmo();
       * customEmitterGizmo.configure({
       *     showRadius: true,
       *     showDirections: true,
       *     directionColor: Color.BLUE
       * });
       * GizmoManager.registerDrawer(customEmitterGizmo);
       * ```
       * 
       * Creating custom gizmo drawers:
       * ```typescript
       * import { GizmoDrawer } from "./Game/gizmos";
       * import { MyCustomComponent } from "./MyCustomComponent";
       * 
       * class MyCustomGizmo extends GizmoDrawer<MyCustomComponent> {
       *     public readonly componentType = MyCustomComponent;
       *     public readonly drawerName = "MyCustomGizmo";
       *     
       *     public drawGizmos(component: MyCustomComponent, graphics: Graphics, node: Node): void {
       *         // Your custom drawing logic here
       *         this.drawCross(graphics, 0, 0, 10, Color.GREEN);
       *     }
       * }
       * 
       * // Register your custom drawer
       * GizmoManager.registerDrawer(new MyCustomGizmo());
       * ```
       */


      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=bed35bbc8820bca845de6304e2cd055fa5a66202.js.map