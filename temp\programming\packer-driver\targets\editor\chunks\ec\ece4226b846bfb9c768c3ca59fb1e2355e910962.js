System.register(["__unresolved_0", "cc", "cc/env", "__unresolved_1", "__unresolved_2"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Component, Graphics, EDITOR, autoRegisterGizmoDrawers, _dec, _dec2, _class, _class2, _descriptor, _descriptor2, _descriptor3, _descriptor4, _class3, _crd, ccclass, property, executeInEditMode, GizmoManager;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfGizmoDrawer(extras) {
    _reporterNs.report("GizmoDrawer", "./GizmoDrawer", _context.meta, extras);
  }

  function _reportPossibleCrUseOfautoRegisterGizmoDrawers(extras) {
    _reporterNs.report("autoRegisterGizmoDrawers", "./GizmoDrawer", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Component = _cc.Component;
      Graphics = _cc.Graphics;
    }, function (_ccEnv) {
      EDITOR = _ccEnv.EDITOR;
    }, function (_unresolved_2) {
      autoRegisterGizmoDrawers = _unresolved_2.autoRegisterGizmoDrawers;
    }, function (_unresolved_3) {}],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "35b7e0iBnFHtqqvAd1SurM7", "GizmoManager", undefined);

      __checkObsolete__(['_decorator', 'Component', 'Graphics', 'Node', 'find', 'Color']); // Import gizmo classes to ensure their decorators are executed


      ({
        ccclass,
        property,
        executeInEditMode
      } = _decorator);
      /**
       * Global gizmo manager that handles all gizmo drawing
       * Should be attached to a global node in the scene
       */

      _export("GizmoManager", GizmoManager = (_dec = ccclass('GizmoManager'), _dec2 = executeInEditMode(true), _dec(_class = _dec2(_class = (_class2 = (_class3 = class GizmoManager extends Component {
        constructor(...args) {
          super(...args);

          _initializerDefineProperty(this, "gizmosEnabled", _descriptor, this);

          _initializerDefineProperty(this, "drawInPlayMode", _descriptor2, this);

          _initializerDefineProperty(this, "refreshRate", _descriptor3, this);

          // FPS for gizmo updates
          _initializerDefineProperty(this, "maxDrawDistance", _descriptor4, this);

          // Maximum distance to draw gizmos
          // Graphics component for drawing
          this.graphics = null;
          // Update timer
          this.updateTimer = 0;
          this.updateInterval = 1 / 60;
        }

        onLoad() {
          if (!EDITOR && !this.drawInPlayMode) return; // Set as singleton instance

          GizmoManager.instance = this; // Ensure the gizmo manager node is positioned at origin for proper coordinate mapping

          this.node.setPosition(0, 0, 0);
          this.node.setRotation(0, 0, 0, 1);
          this.node.setScale(1, 1, 1); // Get or create Graphics component

          this.graphics = this.getComponent(Graphics) || this.addComponent(Graphics); // Update refresh interval

          this.updateInterval = 1 / this.refreshRate; // Auto-register all decorated gizmo drawers

          GizmoManager.autoRegisterDrawers();
          console.log('GizmoManager: Initialized with auto-registered drawers');
        }

        onDestroy() {
          if (GizmoManager.instance === this) {
            GizmoManager.instance = null;
          }
        }

        update(deltaTime) {
          if (!EDITOR && !this.drawInPlayMode) return;
          if (!this.graphics) return; // Throttle updates based on refresh rate

          this.updateTimer += deltaTime;
          if (this.updateTimer < this.updateInterval) return;
          this.updateTimer = 0;

          if (this.gizmosEnabled) {
            this.drawAllGizmos();
          } else {
            // Clear graphics when gizmos are disabled
            this.graphics.clear();
          }
        }
        /**
         * Draw all gizmos for all registered drawers
         */


        drawAllGizmos() {
          if (!this.graphics) return; // Clear previous drawings

          this.graphics.clear(); // Get all drawers sorted by priority

          const sortedDrawers = Array.from(GizmoManager.drawers.values()).filter(drawer => drawer.enabled).sort((a, b) => a.getPriority() - b.getPriority()); // Find all nodes in the scene

          const rootNodes = this.findAllRootNodes(); // Draw gizmos for each drawer

          for (const drawer of sortedDrawers) {
            this.drawGizmosForDrawer(drawer, rootNodes);
          }
        }
        /**
         * Draw gizmos for a specific drawer
         */


        drawGizmosForDrawer(drawer, rootNodes) {
          if (!this.graphics) return;
          const componentsToProcess = []; // Find all components of the drawer's type

          for (const rootNode of rootNodes) {
            this.findComponentsRecursive(rootNode, drawer, componentsToProcess);
          } // Draw gizmos for each component


          for (const {
            component,
            node
          } of componentsToProcess) {
            try {
              drawer.drawGizmos(component, this.graphics, node);
            } catch (error) {
              console.error(`GizmoManager: Error drawing gizmos for ${drawer.drawerName}:`, error);
            }
          }
        }
        /**
         * Recursively find components that match the drawer's type
         */


        findComponentsRecursive(node, drawer, results) {
          // Check distance from gizmo manager
          const distance = node.worldPosition.subtract(this.node.worldPosition).length();
          if (distance > this.maxDrawDistance) return; // Check components on this node

          const components = node.getComponents(Component);

          for (const component of components) {
            if (drawer.canHandle(component)) {
              results.push({
                component,
                node
              });
            }
          } // Recursively check children


          for (const child of node.children) {
            this.findComponentsRecursive(child, drawer, results);
          }
        }
        /**
         * Find all root nodes in the scene
         */


        findAllRootNodes() {
          const scene = this.node.scene;
          if (!scene) return [];
          return scene.children.filter(child => child !== this.node);
        }
        /**
         * Register a gizmo drawer
         */


        static registerDrawer(drawer) {
          const key = drawer.drawerName;

          if (GizmoManager.drawers.has(key)) {
            console.warn(`GizmoManager: Drawer ${key} is already registered`);
            return;
          }

          GizmoManager.drawers.set(key, drawer);
          drawer.onRegister();

          if (EDITOR) {
            console.log(`GizmoManager: Registered drawer ${key}`);
          }
        }
        /**
         * Unregister a gizmo drawer
         */


        static unregisterDrawer(drawerName) {
          const drawer = GizmoManager.drawers.get(drawerName);
          if (!drawer) return false;
          drawer.onUnregister();
          GizmoManager.drawers.delete(drawerName);

          if (EDITOR) {
            console.log(`GizmoManager: Unregistered drawer ${drawerName}`);
          }

          return true;
        }
        /**
         * Get a registered drawer by name
         */


        static getDrawer(drawerName) {
          return GizmoManager.drawers.get(drawerName) || null;
        }
        /**
         * Get all registered drawers
         */


        static getAllDrawers() {
          return Array.from(GizmoManager.drawers.values());
        }
        /**
         * Get the singleton instance
         */


        static getInstance() {
          return GizmoManager.instance;
        }
        /**
         * Auto-register all decorated gizmo drawers
         */


        static autoRegisterDrawers() {
          (_crd && autoRegisterGizmoDrawers === void 0 ? (_reportPossibleCrUseOfautoRegisterGizmoDrawers({
            error: Error()
          }), autoRegisterGizmoDrawers) : autoRegisterGizmoDrawers)(drawer => {
            GizmoManager.registerDrawer(drawer);
          });
        }
        /**
         * Force refresh all gizmos
         */


        static refresh() {
          const instance = GizmoManager.getInstance();

          if (instance) {
            instance.drawAllGizmos();
          }
        }

      }, _class3.drawers = new Map(), _class3.instance = null, _class3), (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "gizmosEnabled", [property], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return true;
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "drawInPlayMode", [property], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return false;
        }
      }), _descriptor3 = _applyDecoratedDescriptor(_class2.prototype, "refreshRate", [property], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return 60;
        }
      }), _descriptor4 = _applyDecoratedDescriptor(_class2.prototype, "maxDrawDistance", [property], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return 2000;
        }
      })), _class2)) || _class) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=ece4226b846bfb9c768c3ca59fb1e2355e910962.js.map