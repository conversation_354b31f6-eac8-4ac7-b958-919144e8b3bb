System.register([], function (_export, _context) {
  "use strict";

  return {
    setters: [],
    execute: function () {
      // This module is auto-generated to report error emitted when try to load module file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/gizmos/EmitterGizmo.ts at runtime.
      throw new Error(`Error: Error when fetching file D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\gizmos\EmitterGizmo.ts: Error: ENOENT: no such file or directory, open 'D:\Workspace\Projects\Moolego\M2Game\Client\assets\scripts\Game\gizmos\EmitterGizmo.ts'`);
    }
  };
});
//# sourceMappingURL=bbe278795380bec38d1d0ae3f7e814e32f0b51d3.js.map