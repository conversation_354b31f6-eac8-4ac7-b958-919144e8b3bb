{"version": 3, "sources": ["file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/gizmos/EmitterArcGizmo.ts"], "names": ["Color", "Vec3", "GizmoDrawer", "RegisterGizmoDrawer", "Gizmo<PERSON><PERSON>s", "EmitterArc", "EmitterArcGizmo", "componentType", "drawerName", "showRadius", "showDirections", "showCenter", "showArc", "radiusColor", "GRAY", "directionColor", "RED", "centerColor", "WHITE", "arcColor", "YELLOW", "speedScale", "arrowSize", "centerSize", "drawGizmos", "emitter", "graphics", "node", "gizmoPos", "worldToGizmoSpace", "worldPosition", "gizmoX", "x", "gizmoY", "y", "drawCenter", "radius", "drawRadius", "arc", "drawArcIndicator", "count", "drawDirections", "worldPos", "gizmoNode", "localPos", "inverseTransformPoint", "worldX", "worldY", "drawCross", "drawCircle", "strokeColor", "lineWidth", "baseAngleRad", "angle", "Math", "PI", "arcRad", "startAngle", "endAngle", "arcStartRadius", "baseLength", "speedFactor", "speedMultiplier", "<PERSON><PERSON><PERSON><PERSON>", "max", "arcEndRadius", "segments", "floor", "i", "cos", "sin", "moveTo", "lineTo", "startSpawnX", "startSpawnY", "endSpawnX", "endSpawnY", "startEndX", "startEndY", "endEndX", "endEndY", "stroke", "<PERSON><PERSON><PERSON><PERSON>", "direction", "getDirection", "spawnPos", "getSpawnPosition", "startX", "startY", "endX", "endY", "drawArrow", "getPriority", "configure", "options", "undefined"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAA+BA,MAAAA,K,OAAAA,K;AAAaC,MAAAA,I,OAAAA,I;;AACnCC,MAAAA,W,iBAAAA,W;AAAaC,MAAAA,mB,iBAAAA,mB;;AACbC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,U,iBAAAA,U;;;;;;;;;AAET;AACA;AACA;AACA;iCAEaC,e;;oEADb,MACaA,eADb;AAAA;AAAA,sCAC6D;AAAA;AAAA;AAAA,eAEzCC,aAFyC;AAAA;AAAA;AAAA,eAGzCC,UAHyC,GAG5B,iBAH4B;AAKzD;AALyD,eAMlDC,UANkD,GAM5B,IAN4B;AAAA,eAOlDC,cAPkD,GAOxB,IAPwB;AAAA,eAQlDC,UARkD,GAQ5B,IAR4B;AAAA,eASlDC,OATkD,GAS/B,IAT+B;AAWzD;AAXyD,eAYlDC,WAZkD,GAY7Bb,KAAK,CAACc,IAZuB;AAAA,eAalDC,cAbkD,GAa1Bf,KAAK,CAACgB,GAboB;AAAA,eAclDC,WAdkD,GAc7BjB,KAAK,CAACkB,KAduB;AAAA,eAelDC,QAfkD,GAehCnB,KAAK,CAACoB,MAf0B;AAiBzD;AAjByD,eAkBlDC,UAlBkD,GAkB7B,GAlB6B;AAAA,eAmBlDC,SAnBkD,GAmB9B,CAnB8B;AAAA,eAoBlDC,UApBkD,GAoB7B,CApB6B;AAAA;;AAsBlDC,QAAAA,UAAU,CAACC,OAAD,EAAsBC,QAAtB,EAA0CC,IAA1C,EAA4D;AACzE;AACA,cAAMC,QAAQ,GAAG,KAAKC,iBAAL,CAAuBF,IAAI,CAACG,aAA5B,EAA2CJ,QAAQ,CAACC,IAApD,CAAjB;AACA,cAAMI,MAAM,GAAGH,QAAQ,CAACI,CAAxB;AACA,cAAMC,MAAM,GAAGL,QAAQ,CAACM,CAAxB,CAJyE,CAMzE;;AACA,cAAI,KAAKvB,UAAT,EAAqB;AACjB,iBAAKwB,UAAL,CAAgBT,QAAhB,EAA0BK,MAA1B,EAAkCE,MAAlC;AACH,WATwE,CAWzE;;;AACA,cAAI,KAAKxB,UAAL,IAAmBgB,OAAO,CAACW,MAAR,GAAiB,CAAxC,EAA2C;AACvC,iBAAKC,UAAL,CAAgBX,QAAhB,EAA0BK,MAA1B,EAAkCE,MAAlC,EAA0CR,OAAO,CAACW,MAAlD;AACH,WAdwE,CAgBzE;;;AACA,cAAI,KAAKxB,OAAL,IAAgBa,OAAO,CAACa,GAAR,GAAc,CAAlC,EAAqC;AACjC,iBAAKC,gBAAL,CAAsBb,QAAtB,EAAgCD,OAAhC,EAAyCM,MAAzC,EAAiDE,MAAjD;AACH,WAnBwE,CAqBzE;;;AACA,cAAI,KAAKvB,cAAL,IAAuBe,OAAO,CAACe,KAAR,GAAgB,CAA3C,EAA8C;AAC1C,iBAAKC,cAAL,CAAoBf,QAApB,EAA8BD,OAA9B,EAAuCM,MAAvC,EAA+CE,MAA/C;AACH;AACJ;AAED;AACJ;AACA;AACA;;;AACYJ,QAAAA,iBAAiB,CAACa,QAAD,EAA2BC,SAA3B,EAAsE;AAC3F;AACA,cAAMC,QAAQ,GAAG,IAAI3C,IAAJ,EAAjB;AACA0C,UAAAA,SAAS,CAACE,qBAAV,CAAgCD,QAAhC,EAA0CF,QAA1C;AACA,iBAAO;AAAEV,YAAAA,CAAC,EAAEY,QAAQ,CAACZ,CAAd;AAAiBE,YAAAA,CAAC,EAAEU,QAAQ,CAACV;AAA7B,WAAP;AACH;;AAEOC,QAAAA,UAAU,CAACT,QAAD,EAAqBoB,MAArB,EAAqCC,MAArC,EAA2D;AACzE;AAAA;AAAA,wCAAWC,SAAX,CAAqBtB,QAArB,EAA+BoB,MAA/B,EAAuCC,MAAvC,EAA+C,KAAKxB,UAApD,EAAgE,KAAKN,WAArE;AACH;;AAEOoB,QAAAA,UAAU,CAACX,QAAD,EAAqBoB,MAArB,EAAqCC,MAArC,EAAqDX,MAArD,EAA2E;AACzF;AAAA;AAAA,wCAAWa,UAAX,CAAsBvB,QAAtB,EAAgCoB,MAAhC,EAAwCC,MAAxC,EAAgDX,MAAhD,EAAwD,KAAKvB,WAA7D,EAA0E,KAA1E;AACH;;AAEO0B,QAAAA,gBAAgB,CAACb,QAAD,EAAqBD,OAArB,EAA0CqB,MAA1C,EAA0DC,MAA1D,EAAgF;AACpG,cAAItB,OAAO,CAACa,GAAR,IAAe,CAAnB,EAAsB;AAEtBZ,UAAAA,QAAQ,CAACwB,WAAT,GAAuB,KAAK/B,QAA5B;AACAO,UAAAA,QAAQ,CAACyB,SAAT,GAAqB,CAArB,CAJoG,CAMpG;AACA;;AACA,cAAMC,YAAY,GAAG3B,OAAO,CAAC4B,KAAR,GAAgBC,IAAI,CAACC,EAArB,GAA0B,GAA/C;AACA,cAAMC,MAAM,GAAG/B,OAAO,CAACa,GAAR,GAAcgB,IAAI,CAACC,EAAnB,GAAwB,GAAvC;AAEA,cAAME,UAAU,GAAGL,YAAY,GAAGI,MAAM,GAAG,CAA3C;AACA,cAAME,QAAQ,GAAGN,YAAY,GAAGI,MAAM,GAAG,CAAzC,CAZoG,CAcpG;;AACA,cAAMG,cAAc,GAAGlC,OAAO,CAACW,MAA/B,CAfoG,CAe7D;AACvC;;AACA,cAAMwB,UAAU,GAAG,EAAnB;AACA,cAAMC,WAAW,GAAGpC,OAAO,CAACqC,eAAR,IAA2B,CAA/C;AACA,cAAMC,SAAS,GAAGT,IAAI,CAACU,GAAL,CAASJ,UAAT,EAAqBA,UAAU,GAAGC,WAAb,GAA2B,KAAKxC,UAArD,CAAlB;AACA,cAAM4C,YAAY,GAAGN,cAAc,GAAGI,SAAtC,CApBoG,CAsBpG;;AACA,cAAMG,QAAQ,GAAGZ,IAAI,CAACU,GAAL,CAAS,CAAT,EAAYV,IAAI,CAACa,KAAL,CAAW1C,OAAO,CAACa,GAAR,GAAc,CAAzB,CAAZ,CAAjB,CAvBoG,CAuBzC;AAE3D;;AACA,eAAK,IAAI8B,CAAC,GAAG,CAAb,EAAgBA,CAAC,IAAIF,QAArB,EAA+BE,CAAC,EAAhC,EAAoC;AAChC,gBAAMf,KAAK,GAAGI,UAAU,GAAG,CAACC,QAAQ,GAAGD,UAAZ,KAA2BW,CAAC,GAAGF,QAA/B,CAA3B;AACA,gBAAMlC,CAAC,GAAGc,MAAM,GAAGQ,IAAI,CAACe,GAAL,CAAShB,KAAT,IAAkBY,YAArC;AACA,gBAAM/B,CAAC,GAAGa,MAAM,GAAGO,IAAI,CAACgB,GAAL,CAASjB,KAAT,IAAkBY,YAArC;;AAEA,gBAAIG,CAAC,KAAK,CAAV,EAAa;AACT1C,cAAAA,QAAQ,CAAC6C,MAAT,CAAgBvC,CAAhB,EAAmBE,CAAnB;AACH,aAFD,MAEO;AACHR,cAAAA,QAAQ,CAAC8C,MAAT,CAAgBxC,CAAhB,EAAmBE,CAAnB;AACH;AACJ,WApCmG,CAsCpG;;;AACA,cAAMuC,WAAW,GAAG3B,MAAM,GAAGQ,IAAI,CAACe,GAAL,CAASZ,UAAT,IAAuBE,cAApD;AACA,cAAMe,WAAW,GAAG3B,MAAM,GAAGO,IAAI,CAACgB,GAAL,CAASb,UAAT,IAAuBE,cAApD;AACA,cAAMgB,SAAS,GAAG7B,MAAM,GAAGQ,IAAI,CAACe,GAAL,CAASX,QAAT,IAAqBC,cAAhD;AACA,cAAMiB,SAAS,GAAG7B,MAAM,GAAGO,IAAI,CAACgB,GAAL,CAASZ,QAAT,IAAqBC,cAAhD;AAEA,cAAMkB,SAAS,GAAG/B,MAAM,GAAGQ,IAAI,CAACe,GAAL,CAASZ,UAAT,IAAuBQ,YAAlD;AACA,cAAMa,SAAS,GAAG/B,MAAM,GAAGO,IAAI,CAACgB,GAAL,CAASb,UAAT,IAAuBQ,YAAlD;AACA,cAAMc,OAAO,GAAGjC,MAAM,GAAGQ,IAAI,CAACe,GAAL,CAASX,QAAT,IAAqBO,YAA9C;AACA,cAAMe,OAAO,GAAGjC,MAAM,GAAGO,IAAI,CAACgB,GAAL,CAASZ,QAAT,IAAqBO,YAA9C,CA/CoG,CAiDpG;;AACAvC,UAAAA,QAAQ,CAAC6C,MAAT,CAAgBE,WAAhB,EAA6BC,WAA7B;AACAhD,UAAAA,QAAQ,CAAC8C,MAAT,CAAgBK,SAAhB,EAA2BC,SAA3B;AACApD,UAAAA,QAAQ,CAAC6C,MAAT,CAAgBI,SAAhB,EAA2BC,SAA3B;AACAlD,UAAAA,QAAQ,CAAC8C,MAAT,CAAgBO,OAAhB,EAAyBC,OAAzB;AAEAtD,UAAAA,QAAQ,CAACuD,MAAT;AACH;;AAEOxC,QAAAA,cAAc,CAACf,QAAD,EAAqBD,OAArB,EAA0CqB,MAA1C,EAA0DC,MAA1D,EAAgF;AAClG,cAAMa,UAAU,GAAG,EAAnB;AACA,cAAMC,WAAW,GAAGpC,OAAO,CAACqC,eAAR,IAA2B,CAA/C;AACA,cAAMoB,WAAW,GAAG5B,IAAI,CAACU,GAAL,CAASJ,UAAT,EAAqBA,UAAU,GAAGC,WAAb,GAA2B,KAAKxC,UAArD,CAApB;;AAEA,eAAK,IAAI+C,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG3C,OAAO,CAACe,KAA5B,EAAmC4B,CAAC,EAApC,EAAwC;AACpC,gBAAMe,SAAS,GAAG1D,OAAO,CAAC2D,YAAR,CAAqBhB,CAArB,CAAlB;AACA,gBAAMiB,QAAQ,GAAG5D,OAAO,CAAC6D,gBAAR,CAAyBlB,CAAzB,CAAjB,CAFoC,CAIpC;;AACA,gBAAMmB,MAAM,GAAGzC,MAAM,GAAGuC,QAAQ,CAACrD,CAAjC;AACA,gBAAMwD,MAAM,GAAGzC,MAAM,GAAGsC,QAAQ,CAACnD,CAAjC,CANoC,CAQpC;;AACA,gBAAMuD,IAAI,GAAGF,MAAM,GAAGJ,SAAS,CAACnD,CAAV,GAAckD,WAApC;AACA,gBAAMQ,IAAI,GAAGF,MAAM,GAAGL,SAAS,CAACjD,CAAV,GAAcgD,WAApC,CAVoC,CAYpC;;AACA;AAAA;AAAA,0CAAWS,SAAX,CAAqBjE,QAArB,EAA+B6D,MAA/B,EAAuCC,MAAvC,EAA+CC,IAA/C,EAAqDC,IAArD,EAA2D,KAAK3E,cAAhE,EAAgF,KAAKO,SAArF;AACH;AACJ;;AAEMsE,QAAAA,WAAW,GAAW;AACzB,iBAAO,EAAP,CADyB,CACd;AACd;AAED;AACJ;AACA;;;AACWC,QAAAA,SAAS,CAACC,OAAD,EAYP;AACL,cAAIA,OAAO,CAACrF,UAAR,KAAuBsF,SAA3B,EAAsC,KAAKtF,UAAL,GAAkBqF,OAAO,CAACrF,UAA1B;AACtC,cAAIqF,OAAO,CAACpF,cAAR,KAA2BqF,SAA/B,EAA0C,KAAKrF,cAAL,GAAsBoF,OAAO,CAACpF,cAA9B;AAC1C,cAAIoF,OAAO,CAACnF,UAAR,KAAuBoF,SAA3B,EAAsC,KAAKpF,UAAL,GAAkBmF,OAAO,CAACnF,UAA1B;AACtC,cAAImF,OAAO,CAAClF,OAAR,KAAoBmF,SAAxB,EAAmC,KAAKnF,OAAL,GAAekF,OAAO,CAAClF,OAAvB;AACnC,cAAIkF,OAAO,CAACjF,WAAR,KAAwBkF,SAA5B,EAAuC,KAAKlF,WAAL,GAAmBiF,OAAO,CAACjF,WAA3B;AACvC,cAAIiF,OAAO,CAAC/E,cAAR,KAA2BgF,SAA/B,EAA0C,KAAKhF,cAAL,GAAsB+E,OAAO,CAAC/E,cAA9B;AAC1C,cAAI+E,OAAO,CAAC7E,WAAR,KAAwB8E,SAA5B,EAAuC,KAAK9E,WAAL,GAAmB6E,OAAO,CAAC7E,WAA3B;AACvC,cAAI6E,OAAO,CAAC3E,QAAR,KAAqB4E,SAAzB,EAAoC,KAAK5E,QAAL,GAAgB2E,OAAO,CAAC3E,QAAxB;AACpC,cAAI2E,OAAO,CAACzE,UAAR,KAAuB0E,SAA3B,EAAsC,KAAK1E,UAAL,GAAkByE,OAAO,CAACzE,UAA1B;AACtC,cAAIyE,OAAO,CAACxE,SAAR,KAAsByE,SAA1B,EAAqC,KAAKzE,SAAL,GAAiBwE,OAAO,CAACxE,SAAzB;AACrC,cAAIwE,OAAO,CAACvE,UAAR,KAAuBwE,SAA3B,EAAsC,KAAKxE,UAAL,GAAkBuE,OAAO,CAACvE,UAA1B;AACzC;;AAnLwD,O", "sourcesContent": ["import { _decorator, Graphics, Color, Node, Vec3 } from 'cc';\nimport { GizmoDrawer, RegisterGizmoDrawer } from './GizmoDrawer';\nimport { GizmoUtils } from './GizmoUtils';\nimport { EmitterArc } from '../world/bullet/EmitterArc';\n\n/**\n * Gizmo drawer for EmitterArc components\n * Draws visual debugging information for arc-based bullet emitters\n */\n@RegisterGizmoDrawer\nexport class EmitterArcGizmo extends GizmoDrawer<EmitterArc> {\n    \n    public readonly componentType = EmitterArc;\n    public readonly drawerName = \"EmitterArcGizmo\";\n    \n    // Gizmo display options\n    public showRadius: boolean = true;\n    public showDirections: boolean = true;\n    public showCenter: boolean = true;\n    public showArc: boolean = true;\n    \n    // Colors\n    public radiusColor: Color = Color.GRAY;\n    public directionColor: Color = Color.RED;\n    public centerColor: Color = Color.WHITE;\n    public arcColor: Color = Color.YELLOW;\n    \n    // Display settings\n    public speedScale: number = 1.0;\n    public arrowSize: number = 8;\n    public centerSize: number = 8;\n    \n    public drawGizmos(emitter: EmitterArc, graphics: Graphics, node: Node): void {\n        // For 2D projects, convert world position to graphics local space\n        const gizmoPos = this.worldToGizmoSpace(node.worldPosition, graphics.node);\n        const gizmoX = gizmoPos.x;\n        const gizmoY = gizmoPos.y;\n\n        // Draw center point\n        if (this.showCenter) {\n            this.drawCenter(graphics, gizmoX, gizmoY);\n        }\n\n        // Draw radius circle\n        if (this.showRadius && emitter.radius > 0) {\n            this.drawRadius(graphics, gizmoX, gizmoY, emitter.radius);\n        }\n\n        // Draw arc indicator\n        if (this.showArc && emitter.arc > 0) {\n            this.drawArcIndicator(graphics, emitter, gizmoX, gizmoY);\n        }\n\n        // Draw direction arrows\n        if (this.showDirections && emitter.count > 0) {\n            this.drawDirections(graphics, emitter, gizmoX, gizmoY);\n        }\n    }\n\n    /**\n     * Convert world position to gizmo graphics coordinate space\n     * For 2D projects, this converts world coordinates to the local space of the graphics node\n     */\n    private worldToGizmoSpace(worldPos: Readonly<Vec3>, gizmoNode: Node): { x: number, y: number } {\n        // Convert world position to local position of the gizmo graphics node\n        const localPos = new Vec3();\n        gizmoNode.inverseTransformPoint(localPos, worldPos);\n        return { x: localPos.x, y: localPos.y };\n    }\n    \n    private drawCenter(graphics: Graphics, worldX: number, worldY: number): void {\n        GizmoUtils.drawCross(graphics, worldX, worldY, this.centerSize, this.centerColor);\n    }\n\n    private drawRadius(graphics: Graphics, worldX: number, worldY: number, radius: number): void {\n        GizmoUtils.drawCircle(graphics, worldX, worldY, radius, this.radiusColor, false);\n    }\n    \n    private drawArcIndicator(graphics: Graphics, emitter: EmitterArc, worldX: number, worldY: number): void {\n        if (emitter.arc <= 0) return;\n\n        graphics.strokeColor = this.arcColor;\n        graphics.lineWidth = 2;\n\n        // Convert angle and arc to radians\n        // Use the same coordinate system as EmitterArc.getDirection() - no +90 offset\n        const baseAngleRad = emitter.angle * Math.PI / 180;\n        const arcRad = emitter.arc * Math.PI / 180;\n\n        const startAngle = baseAngleRad - arcRad / 2;\n        const endAngle = baseAngleRad + arcRad / 2;\n\n        // Draw arc starting from the emitter radius (spawn position) extending outward\n        const arcStartRadius = emitter.radius; // Start from spawn radius\n        // Use same length calculation as direction arrows for consistency\n        const baseLength = 30;\n        const speedFactor = emitter.speedMultiplier || 1;\n        const arcLength = Math.max(baseLength, baseLength * speedFactor * this.speedScale);\n        const arcEndRadius = arcStartRadius + arcLength;\n\n        // Draw arc lines from spawn radius extending outward to show direction range\n        const segments = Math.max(8, Math.floor(emitter.arc / 5)); // More segments for larger arcs\n\n        // Draw the arc at the end radius to show the direction spread\n        for (let i = 0; i <= segments; i++) {\n            const angle = startAngle + (endAngle - startAngle) * (i / segments);\n            const x = worldX + Math.cos(angle) * arcEndRadius;\n            const y = worldY + Math.sin(angle) * arcEndRadius;\n\n            if (i === 0) {\n                graphics.moveTo(x, y);\n            } else {\n                graphics.lineTo(x, y);\n            }\n        }\n\n        // Draw lines from spawn position to end of arc to show the direction range\n        const startSpawnX = worldX + Math.cos(startAngle) * arcStartRadius;\n        const startSpawnY = worldY + Math.sin(startAngle) * arcStartRadius;\n        const endSpawnX = worldX + Math.cos(endAngle) * arcStartRadius;\n        const endSpawnY = worldY + Math.sin(endAngle) * arcStartRadius;\n\n        const startEndX = worldX + Math.cos(startAngle) * arcEndRadius;\n        const startEndY = worldY + Math.sin(startAngle) * arcEndRadius;\n        const endEndX = worldX + Math.cos(endAngle) * arcEndRadius;\n        const endEndY = worldY + Math.sin(endAngle) * arcEndRadius;\n\n        // Draw lines from spawn radius to end radius for arc boundaries\n        graphics.moveTo(startSpawnX, startSpawnY);\n        graphics.lineTo(startEndX, startEndY);\n        graphics.moveTo(endSpawnX, endSpawnY);\n        graphics.lineTo(endEndX, endEndY);\n\n        graphics.stroke();\n    }\n    \n    private drawDirections(graphics: Graphics, emitter: EmitterArc, worldX: number, worldY: number): void {\n        const baseLength = 30;\n        const speedFactor = emitter.speedMultiplier || 1;\n        const arrowLength = Math.max(baseLength, baseLength * speedFactor * this.speedScale);\n\n        for (let i = 0; i < emitter.count; i++) {\n            const direction = emitter.getDirection(i);\n            const spawnPos = emitter.getSpawnPosition(i);\n\n            // Start position (at spawn position relative to world position)\n            const startX = worldX + spawnPos.x;\n            const startY = worldY + spawnPos.y;\n\n            // End position (direction from spawn position)\n            const endX = startX + direction.x * arrowLength;\n            const endY = startY + direction.y * arrowLength;\n\n            // Draw arrow\n            GizmoUtils.drawArrow(graphics, startX, startY, endX, endY, this.directionColor, this.arrowSize);\n        }\n    }\n    \n    public getPriority(): number {\n        return 10; // Draw emitter gizmos with medium priority\n    }\n    \n    /**\n     * Configure display options\n     */\n    public configure(options: {\n        showRadius?: boolean;\n        showDirections?: boolean;\n        showCenter?: boolean;\n        showArc?: boolean;\n        radiusColor?: Color;\n        directionColor?: Color;\n        centerColor?: Color;\n        arcColor?: Color;\n        speedScale?: number;\n        arrowSize?: number;\n        centerSize?: number;\n    }): void {\n        if (options.showRadius !== undefined) this.showRadius = options.showRadius;\n        if (options.showDirections !== undefined) this.showDirections = options.showDirections;\n        if (options.showCenter !== undefined) this.showCenter = options.showCenter;\n        if (options.showArc !== undefined) this.showArc = options.showArc;\n        if (options.radiusColor !== undefined) this.radiusColor = options.radiusColor;\n        if (options.directionColor !== undefined) this.directionColor = options.directionColor;\n        if (options.centerColor !== undefined) this.centerColor = options.centerColor;\n        if (options.arcColor !== undefined) this.arcColor = options.arcColor;\n        if (options.speedScale !== undefined) this.speedScale = options.speedScale;\n        if (options.arrowSize !== undefined) this.arrowSize = options.arrowSize;\n        if (options.centerSize !== undefined) this.centerSize = options.centerSize;\n    }\n}\n"]}